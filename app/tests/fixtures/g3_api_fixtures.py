"""
Fixtures and utilities for G3 API integration testing.

Provides:
- Mock G3 API responses
- Test data factories
- Authentication mocking utilities
- Error scenario generators
"""

import pytest
import pandas as pd
from datetime import datetime, timedelta
from unittest.mock import Mock, patch
from typing import Dict, List, Any

from src.common.g3_any_api_service import (
    G3AnyApiService,
    G3AnyApiGlobalService,
    InputProcessingRequest,
    PropertyIdRequest,
    PaceAccomActivityRequest
)
from src.common.http_service import HttpService
from src.common.http_authorizers.g3_auth import G3Auth


class G3ApiMockFactory:
    """Factory for creating G3 API mock responses and test data."""

    @staticmethod
    def create_input_processing_response(
        client_code: str = 'Hilton',
        property_code: str = 'TPANH',
        num_records: int = 3,
        timezone: str = 'America/New_York'
    ) -> List[List[str]]:
        """Create mock input processing response data."""
        base_date = datetime(2024, 1, 15, 10, 0, 0)
        processing_types = ['CDP', 'BDE', 'CDP_DYNAMIC', 'CDP_ON_DEMAND']
        
        return [
            [
                client_code,
                property_code,
                processing_types[i % len(processing_types)],
                (base_date + timedelta(hours=i)).strftime('%Y-%m-%d %H:%M:%S'),
                timezone
            ]
            for i in range(num_records)
        ]

    @staticmethod
    def create_property_id_response(
        client_code: str = 'Hilton',
        property_code: str = 'TPANH',
        property_id: str = '12345'
    ) -> List[List[str]]:
        """Create mock property ID response data."""
        return [[client_code, property_code, property_id]]

    @staticmethod
    def create_pace_accom_response(
        num_days: int = 3,
        num_accom_classes: int = 2,
        base_date: datetime = None
    ) -> List[List[Any]]:
        """Create mock pace accommodation activity response data."""
        if base_date is None:
            base_date = datetime(2024, 1, 15)
        
        accom_classes = ['STD', 'SUP', 'DLX']
        lead_times = [7, 14, 21, 30]
        
        data = []
        for day in range(num_days):
            occupancy_date = (base_date + timedelta(days=day)).strftime('%Y-%m-%d')
            for accom_idx in range(num_accom_classes):
                for lead_time in lead_times[:2]:  # Use first 2 lead times
                    accom_class = accom_classes[accom_idx % len(accom_classes)]
                    capacity = 100 + (accom_idx * 20)
                    sold = capacity - (15 + (day * 2))
                    available = capacity - sold
                    
                    data.append([
                        occupancy_date,
                        lead_time,
                        accom_class,
                        capacity,
                        sold,
                        available
                    ])
        
        return data

    @staticmethod
    def create_empty_response() -> List[List[Any]]:
        """Create empty response for testing edge cases."""
        return []

    @staticmethod
    def create_malformed_response() -> str:
        """Create malformed response for error testing."""
        return "invalid_json_response"


class G3ApiTestFixtures:
    """Test fixtures for G3 API integration tests."""

    @pytest.fixture
    def mock_g3_auth(self):
        """Mock G3 authentication."""
        return Mock(spec=G3Auth)

    @pytest.fixture
    def mock_http_service(self):
        """Mock HTTP service for G3 API calls."""
        return Mock(spec=HttpService)

    @pytest.fixture
    def g3_api_service(self, mock_http_service):
        """G3AnyApiService instance with mocked HTTP service."""
        return G3AnyApiService(mock_http_service, '12345')

    @pytest.fixture
    def g3_api_global_service(self, mock_http_service):
        """G3AnyApiGlobalService instance with mocked HTTP service."""
        return G3AnyApiGlobalService(mock_http_service, 'Hilton')

    @pytest.fixture
    def sample_input_processing_data(self):
        """Sample input processing data."""
        return G3ApiMockFactory.create_input_processing_response()

    @pytest.fixture
    def sample_property_id_data(self):
        """Sample property ID data."""
        return G3ApiMockFactory.create_property_id_response()

    @pytest.fixture
    def sample_pace_accom_data(self):
        """Sample pace accommodation data."""
        return G3ApiMockFactory.create_pace_accom_response()

    @pytest.fixture
    def empty_g3_response(self):
        """Empty G3 API response."""
        return G3ApiMockFactory.create_empty_response()

    @pytest.fixture
    def malformed_g3_response(self):
        """Malformed G3 API response."""
        return G3ApiMockFactory.create_malformed_response()


class G3ApiMockContextManager:
    """Context manager for mocking G3 API services in tests."""

    def __init__(
        self,
        input_processing_data: List[List[str]] = None,
        property_id_data: List[List[str]] = None,
        pace_accom_data: List[List[Any]] = None,
        should_raise_error: Exception = None,
        g3_host_url: str = 'https://g3.example.com'
    ):
        self.input_processing_data = input_processing_data or G3ApiMockFactory.create_input_processing_response()
        self.property_id_data = property_id_data or G3ApiMockFactory.create_property_id_response()
        self.pace_accom_data = pace_accom_data or G3ApiMockFactory.create_pace_accom_response()
        self.should_raise_error = should_raise_error
        self.g3_host_url = g3_host_url
        self.patches = []

    def __enter__(self):
        """Enter the context and set up mocks."""
        # Mock UPS service to return G3 host URL
        ups_patch = patch('src.common.ups_service.get_property_host_url')
        mock_get_host_url = ups_patch.start()
        mock_get_host_url.return_value = self.g3_host_url
        self.patches.append(ups_patch)

        # Mock HTTP service
        http_patch = patch('src.common.http_service.HttpService')
        mock_http_service_class = http_patch.start()
        mock_http_service = Mock()
        mock_http_service_class.return_value = mock_http_service
        self.patches.append(http_patch)

        # Mock G3 API services
        g3_global_patch = patch('src.common.g3_any_api_service.G3AnyApiGlobalService')
        mock_g3_global_class = g3_global_patch.start()
        mock_g3_global_service = Mock()
        mock_g3_global_class.return_value = mock_g3_global_service
        self.patches.append(g3_global_patch)

        g3_patch = patch('src.common.g3_any_api_service.G3AnyApiService')
        mock_g3_class = g3_patch.start()
        mock_g3_service = Mock()
        mock_g3_class.return_value = mock_g3_service
        self.patches.append(g3_patch)

        # Configure mock responses
        if self.should_raise_error:
            mock_g3_global_service.fetch.side_effect = self.should_raise_error
            mock_g3_service.fetch.side_effect = self.should_raise_error
        else:
            def mock_fetch(request):
                if isinstance(request, InputProcessingRequest):
                    return pd.DataFrame(
                        self.input_processing_data,
                        columns=['clientCode', 'propertyCode', 'processingType', 'preparedDate', 'timeZone']
                    )
                elif isinstance(request, PropertyIdRequest):
                    return pd.DataFrame(
                        self.property_id_data,
                        columns=['clientCode', 'propertyCode', 'propertyId']
                    )
                elif isinstance(request, PaceAccomActivityRequest):
                    return pd.DataFrame(
                        self.pace_accom_data,
                        columns=['occupancyDate', 'leadTime', 'accomClassId', 'accomCapacity', 'roomsSold', 'availableCapacity']
                    )
                else:
                    return pd.DataFrame()

            mock_g3_global_service.fetch.side_effect = mock_fetch
            mock_g3_service.fetch.side_effect = mock_fetch

        return {
            'mock_http_service': mock_http_service,
            'mock_g3_global_service': mock_g3_global_service,
            'mock_g3_service': mock_g3_service,
            'mock_get_host_url': mock_get_host_url
        }

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit the context and clean up mocks."""
        for patch_obj in reversed(self.patches):
            patch_obj.stop()


def create_g3_api_test_scenarios():
    """Create various test scenarios for G3 API testing."""
    return {
        'success_scenario': {
            'input_processing_data': G3ApiMockFactory.create_input_processing_response(num_records=5),
            'property_id_data': G3ApiMockFactory.create_property_id_response(),
            'pace_accom_data': G3ApiMockFactory.create_pace_accom_response(num_days=7),
            'should_raise_error': None
        },
        'empty_data_scenario': {
            'input_processing_data': G3ApiMockFactory.create_empty_response(),
            'property_id_data': G3ApiMockFactory.create_empty_response(),
            'pace_accom_data': G3ApiMockFactory.create_empty_response(),
            'should_raise_error': None
        },
        'auth_failure_scenario': {
            'input_processing_data': None,
            'property_id_data': None,
            'pace_accom_data': None,
            'should_raise_error': Exception("401 Unauthorized")
        },
        'timeout_scenario': {
            'input_processing_data': None,
            'property_id_data': None,
            'pace_accom_data': None,
            'should_raise_error': Exception("Request timed out")
        },
        'large_dataset_scenario': {
            'input_processing_data': G3ApiMockFactory.create_input_processing_response(num_records=100),
            'property_id_data': G3ApiMockFactory.create_property_id_response(),
            'pace_accom_data': G3ApiMockFactory.create_pace_accom_response(num_days=30, num_accom_classes=5),
            'should_raise_error': None
        }
    }


# Pytest fixtures that can be imported by test modules
@pytest.fixture
def g3_api_mock_factory():
    """Provide G3ApiMockFactory for tests."""
    return G3ApiMockFactory


@pytest.fixture
def g3_api_test_scenarios():
    """Provide test scenarios for G3 API testing."""
    return create_g3_api_test_scenarios()


@pytest.fixture
def g3_api_success_mock():
    """Context manager for successful G3 API responses."""
    scenarios = create_g3_api_test_scenarios()
    return G3ApiMockContextManager(**scenarios['success_scenario'])


@pytest.fixture
def g3_api_failure_mock():
    """Context manager for failed G3 API responses."""
    scenarios = create_g3_api_test_scenarios()
    return G3ApiMockContextManager(**scenarios['auth_failure_scenario'])
