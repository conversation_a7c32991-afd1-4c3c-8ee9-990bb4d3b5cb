#!/usr/bin/env python3
"""
Calibration Test Data Factory
Generates realistic test data for calibration workflow integration testing
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import pathlib
import logging

logger = logging.getLogger(__name__)

class CalibrationTestDataFactory:
    """Factory for generating realistic calibration test data"""
    
    def __init__(self, base_date: Optional[datetime] = None):
        self.base_date = base_date or datetime.now() - timedelta(days=30)
        self.accommodation_classes = [1, 2, 3, 4, 5]  # Different room types
        self.lead_times = [0, 1, 3, 7, 14, 21, 30, 60, 90]  # Days in advance
        
    def create_calibration_scenario(self, scenario_name: str) -> Dict[str, pd.DataFrame]:
        """Create a complete calibration test scenario"""
        scenarios = {
            'standard': self._create_standard_scenario,
            'high_demand': self._create_high_demand_scenario,
            'low_demand': self._create_low_demand_scenario,
            'volatile_pricing': self._create_volatile_pricing_scenario,
            'edge_case': self._create_edge_case_scenario
        }
        
        if scenario_name not in scenarios:
            raise ValueError(f"Unknown scenario: {scenario_name}")
        
        return scenarios[scenario_name]()
    
    def _create_standard_scenario(self) -> Dict[str, pd.DataFrame]:
        """Standard calibration scenario with typical hotel data"""
        date_range = pd.date_range(
            start=self.base_date,
            end=self.base_date + timedelta(days=30),
            freq='D'
        )
        
        # Generate delta occupancy data
        delta_occ_data = []
        for occ_date in date_range:
            for capture_date in pd.date_range(occ_date - timedelta(days=90), occ_date, freq='D'):
                for accom_class in self.accommodation_classes:
                    for lead_time in self.lead_times:
                        if capture_date + timedelta(days=lead_time) == occ_date:
                            delta_solds = np.random.poisson(2.5)  # Average 2.5 rooms sold per day
                            available_capacity = np.random.randint(20, 50)
                            
                            delta_occ_data.append({
                                'occupancyDate': occ_date.strftime('%Y-%m-%d'),
                                'captureDate': capture_date.strftime('%Y-%m-%d'),
                                'accomClassId': accom_class,
                                'leadTime': lead_time,
                                'deltaSolds': delta_solds,
                                'availableCapacity': available_capacity
                            })
        
        delta_occ_df = pd.DataFrame(delta_occ_data)
        
        # Generate reference rate data
        ref_rate_data = []
        for occ_date in date_range:
            for capture_date in pd.date_range(occ_date - timedelta(days=90), occ_date, freq='D'):
                for accom_class in self.accommodation_classes:
                    for lead_time in self.lead_times:
                        if capture_date + timedelta(days=lead_time) == occ_date:
                            # Base price varies by accommodation class
                            base_price = 100 + (accom_class * 25)
                            # Add some seasonality and randomness
                            seasonal_factor = 1 + 0.2 * np.sin(2 * np.pi * occ_date.timetuple().tm_yday / 365)
                            price = base_price * seasonal_factor * (1 + np.random.normal(0, 0.1))
                            
                            ref_rate_data.append({
                                'occupancyDate': occ_date.strftime('%Y-%m-%d'),
                                'captureDate': capture_date.strftime('%Y-%m-%d'),
                                'accomClassId': accom_class,
                                'leadTime': lead_time,
                                'price': round(price, 2)
                            })
        
        ref_rate_df = pd.DataFrame(ref_rate_data)
        
        # Generate delta LRV data
        delta_lrv_data = []
        for occ_date in date_range:
            for capture_date in pd.date_range(occ_date - timedelta(days=90), occ_date, freq='D'):
                for accom_class in self.accommodation_classes:
                    for lead_time in self.lead_times:
                        if capture_date + timedelta(days=lead_time) == occ_date:
                            # LRV (Length of Stay Revenue) calculations
                            base_lrv = ref_rate_df[
                                (ref_rate_df['occupancyDate'] == occ_date.strftime('%Y-%m-%d')) &
                                (ref_rate_df['captureDate'] == capture_date.strftime('%Y-%m-%d')) &
                                (ref_rate_df['accomClassId'] == accom_class) &
                                (ref_rate_df['leadTime'] == lead_time)
                            ]['price'].iloc[0] if not ref_rate_df.empty else 150.0
                            
                            lrv = base_lrv * np.random.uniform(0.8, 1.2)  # LRV variation
                            delta_lrv = np.random.normal(0, 5)  # Daily LRV change
                            ceiling_value = lrv * 1.5  # Ceiling is 150% of LRV
                            
                            delta_lrv_data.append({
                                'occupancyDate': occ_date.strftime('%Y-%m-%d'),
                                'captureDate': capture_date.strftime('%Y-%m-%d'),
                                'accomClassId': accom_class,
                                'leadTime': lead_time,
                                'lrv': round(lrv, 2),
                                'deltaLrv': round(delta_lrv, 2),
                                'ceilingValue': round(ceiling_value, 2)
                            })
        
        delta_lrv_df = pd.DataFrame(delta_lrv_data)
        
        return {
            'delta_occ_solds': delta_occ_df,
            'reference_rate': ref_rate_df,
            'delta_lrv': delta_lrv_df
        }
    
    def _create_high_demand_scenario(self) -> Dict[str, pd.DataFrame]:
        """High demand scenario with increased occupancy and pricing"""
        scenario = self._create_standard_scenario()
        
        # Increase delta solds by 50%
        scenario['delta_occ_solds']['deltaSolds'] = (
            scenario['delta_occ_solds']['deltaSolds'] * 1.5
        ).astype(int)
        
        # Increase prices by 30%
        scenario['reference_rate']['price'] = (
            scenario['reference_rate']['price'] * 1.3
        ).round(2)
        
        # Increase LRV accordingly
        scenario['delta_lrv']['lrv'] = (
            scenario['delta_lrv']['lrv'] * 1.3
        ).round(2)
        scenario['delta_lrv']['ceilingValue'] = (
            scenario['delta_lrv']['ceilingValue'] * 1.3
        ).round(2)
        
        return scenario
    
    def _create_low_demand_scenario(self) -> Dict[str, pd.DataFrame]:
        """Low demand scenario with reduced occupancy"""
        scenario = self._create_standard_scenario()
        
        # Reduce delta solds by 60%
        scenario['delta_occ_solds']['deltaSolds'] = (
            scenario['delta_occ_solds']['deltaSolds'] * 0.4
        ).astype(int)
        
        # Reduce prices by 20%
        scenario['reference_rate']['price'] = (
            scenario['reference_rate']['price'] * 0.8
        ).round(2)
        
        # Reduce LRV accordingly
        scenario['delta_lrv']['lrv'] = (
            scenario['delta_lrv']['lrv'] * 0.8
        ).round(2)
        scenario['delta_lrv']['ceilingValue'] = (
            scenario['delta_lrv']['ceilingValue'] * 0.8
        ).round(2)
        
        return scenario
    
    def _create_volatile_pricing_scenario(self) -> Dict[str, pd.DataFrame]:
        """Volatile pricing scenario with high price variations"""
        scenario = self._create_standard_scenario()
        
        # Add high volatility to prices
        price_volatility = np.random.normal(1, 0.3, len(scenario['reference_rate']))
        scenario['reference_rate']['price'] = (
            scenario['reference_rate']['price'] * price_volatility
        ).round(2)
        
        # Add high volatility to delta LRV
        lrv_volatility = np.random.normal(0, 15, len(scenario['delta_lrv']))
        scenario['delta_lrv']['deltaLrv'] = (
            scenario['delta_lrv']['deltaLrv'] + lrv_volatility
        ).round(2)
        
        return scenario
    
    def _create_edge_case_scenario(self) -> Dict[str, pd.DataFrame]:
        """Edge case scenario with minimal data"""
        # Create minimal dataset for edge case testing
        date_range = pd.date_range(
            start=self.base_date,
            end=self.base_date + timedelta(days=3),
            freq='D'
        )
        
        delta_occ_data = []
        ref_rate_data = []
        delta_lrv_data = []
        
        for occ_date in date_range:
            for accom_class in [1, 2]:  # Only 2 accommodation classes
                for lead_time in [0, 7]:  # Only 2 lead times
                    capture_date = occ_date - timedelta(days=lead_time)
                    
                    delta_occ_data.append({
                        'occupancyDate': occ_date.strftime('%Y-%m-%d'),
                        'captureDate': capture_date.strftime('%Y-%m-%d'),
                        'accomClassId': accom_class,
                        'leadTime': lead_time,
                        'deltaSolds': 1,
                        'availableCapacity': 10
                    })
                    
                    ref_rate_data.append({
                        'occupancyDate': occ_date.strftime('%Y-%m-%d'),
                        'captureDate': capture_date.strftime('%Y-%m-%d'),
                        'accomClassId': accom_class,
                        'leadTime': lead_time,
                        'price': 100.0
                    })
                    
                    delta_lrv_data.append({
                        'occupancyDate': occ_date.strftime('%Y-%m-%d'),
                        'captureDate': capture_date.strftime('%Y-%m-%d'),
                        'accomClassId': accom_class,
                        'leadTime': lead_time,
                        'lrv': 100.0,
                        'deltaLrv': 0.0,
                        'ceilingValue': 150.0
                    })
        
        return {
            'delta_occ_solds': pd.DataFrame(delta_occ_data),
            'reference_rate': pd.DataFrame(ref_rate_data),
            'delta_lrv': pd.DataFrame(delta_lrv_data)
        }
    
    def save_scenario_to_s3(self, scenario_data: Dict[str, pd.DataFrame], 
                           s3_bucket: str, client_code: str, property_code: str,
                           calibration_date: str) -> Dict[str, str]:
        """Save scenario data to S3 and return S3 paths"""
        import boto3
        
        s3_paths = {}
        s3_client = boto3.client('s3')
        
        for data_type, df in scenario_data.items():
            # Create S3 key
            s3_key = f"{client_code}/{property_code}/calibration/{calibration_date}/{data_type}.csv"
            
            # Convert DataFrame to CSV
            csv_buffer = df.to_csv(index=False)
            
            # Upload to S3
            s3_client.put_object(
                Bucket=s3_bucket,
                Key=s3_key,
                Body=csv_buffer.encode('utf-8'),
                ContentType='text/csv'
            )
            
            s3_paths[data_type] = f"s3://{s3_bucket}/{s3_key}"
            logger.info(f"Uploaded {data_type} data to {s3_paths[data_type]}")
        
        return s3_paths
    
    def save_scenario_to_local(self, scenario_data: Dict[str, pd.DataFrame],
                              output_dir: pathlib.Path) -> Dict[str, str]:
        """Save scenario data to local files"""
        output_dir.mkdir(parents=True, exist_ok=True)
        
        local_paths = {}
        for data_type, df in scenario_data.items():
            file_path = output_dir / f"{data_type}.csv"
            df.to_csv(file_path, index=False)
            local_paths[data_type] = str(file_path)
            logger.info(f"Saved {data_type} data to {file_path}")
        
        return local_paths

    def create_quantile_functions(self, scenario_data: Dict[str, pd.DataFrame]) -> Dict[int, float]:
        """Create quantile functions for the calibration scenario"""
        quantile_functions = {}

        for accom_class in self.accommodation_classes:
            # Create a simple threshold function based on accommodation class
            base_threshold = 0.5 + (accom_class * 0.1)  # Higher class = higher threshold
            quantile_functions[accom_class] = base_threshold

        return quantile_functions

if __name__ == '__main__':
    # Example usage
    factory = CalibrationTestDataFactory()

    # Create standard scenario
    scenario = factory.create_calibration_scenario('standard')

    # Save to local files
    output_dir = pathlib.Path('test_data/standard_scenario')
    paths = factory.save_scenario_to_local(scenario, output_dir)

    print("Generated test data:")
    for data_type, path in paths.items():
        print(f"  {data_type}: {path}")
        print(f"    Records: {len(scenario[data_type])}")
