#!/usr/bin/env python3
"""
AWS Test Infrastructure Setup Script
Creates test AWS resources for integration testing
"""

import boto3
import logging
from typing import Dict, List
import json

logger = logging.getLogger(__name__)

class AWSTestInfrastructure:
    """Manages AWS test infrastructure for integration testing"""
    
    def __init__(self, region: str = 'us-east-2', test_suffix: str = 'test'):
        self.region = region
        self.test_suffix = test_suffix
        self.dynamodb = boto3.client('dynamodb', region_name=region)
        self.s3 = boto3.client('s3', region_name=region)
        self.sns = boto3.client('sns', region_name=region)
        self.sqs = boto3.client('sqs', region_name=region)
        
    def create_test_infrastructure(self) -> Dict[str, str]:
        """Create all test AWS resources"""
        resources = {}
        
        # Create DynamoDB tables
        resources.update(self._create_dynamodb_tables())
        
        # Create S3 bucket
        resources['s3_bucket'] = self._create_s3_bucket()
        
        # Create SNS topic
        resources['sns_topic_arn'] = self._create_sns_topic()
        
        # Create SQS queues
        resources.update(self._create_sqs_queues())
        
        logger.info(f"Created test infrastructure: {resources}")
        return resources
    
    def _create_dynamodb_tables(self) -> Dict[str, str]:
        """Create test DynamoDB tables"""
        tables = {
            'calibrated_potentials': f'dyn-opt-calibrated-potentials-{self.test_suffix}',
            'idp_window': f'dyn-opt-idp-window-{self.test_suffix}',
            'idp_count': f'dyn-opt-idp-count-{self.test_suffix}',
            'decision_change': f'dyn-opt-decision-change-{self.test_suffix}',
            'available_capacity': f'dyn-opt-available-capacity-{self.test_suffix}',
            'inc_solds': f'dyn-opt-inc-solds-{self.test_suffix}',
        }
        
        # Table schemas
        table_schemas = {
            'calibrated_potentials': {
                'KeySchema': [
                    {'AttributeName': 'clientCode_propertyCode', 'KeyType': 'HASH'},
                    {'AttributeName': 'accomClassId', 'KeyType': 'RANGE'}
                ],
                'AttributeDefinitions': [
                    {'AttributeName': 'clientCode_propertyCode', 'AttributeType': 'S'},
                    {'AttributeName': 'accomClassId', 'AttributeType': 'N'}
                ]
            },
            'idp_window': {
                'KeySchema': [
                    {'AttributeName': 'clientCode_propertyCode', 'KeyType': 'HASH'},
                    {'AttributeName': 'evaluationTime', 'KeyType': 'RANGE'}
                ],
                'AttributeDefinitions': [
                    {'AttributeName': 'clientCode_propertyCode', 'AttributeType': 'S'},
                    {'AttributeName': 'evaluationTime', 'AttributeType': 'S'}
                ]
            },
            'idp_count': {
                'KeySchema': [
                    {'AttributeName': 'clientCode_propertyCode', 'KeyType': 'HASH'},
                    {'AttributeName': 'caughtUpDate', 'KeyType': 'RANGE'}
                ],
                'AttributeDefinitions': [
                    {'AttributeName': 'clientCode_propertyCode', 'AttributeType': 'S'},
                    {'AttributeName': 'caughtUpDate', 'AttributeType': 'S'}
                ]
            }
        }
        
        created_tables = {}
        for table_key, table_name in tables.items():
            try:
                # Check if table exists
                self.dynamodb.describe_table(TableName=table_name)
                logger.info(f"Table {table_name} already exists")
            except self.dynamodb.exceptions.ResourceNotFoundException:
                # Create table
                schema = table_schemas.get(table_key, table_schemas['calibrated_potentials'])
                
                self.dynamodb.create_table(
                    TableName=table_name,
                    KeySchema=schema['KeySchema'],
                    AttributeDefinitions=schema['AttributeDefinitions'],
                    BillingMode='PAY_PER_REQUEST'
                )
                
                # Wait for table to be active
                waiter = self.dynamodb.get_waiter('table_exists')
                waiter.wait(TableName=table_name)
                logger.info(f"Created DynamoDB table: {table_name}")
            
            created_tables[table_key] = table_name
            
        return created_tables
    
    def _create_s3_bucket(self) -> str:
        """Create test S3 bucket"""
        bucket_name = f'dyn-opt-data-{self.test_suffix}'
        
        try:
            self.s3.head_bucket(Bucket=bucket_name)
            logger.info(f"S3 bucket {bucket_name} already exists")
        except:
            # Create bucket
            if self.region == 'us-east-1':
                self.s3.create_bucket(Bucket=bucket_name)
            else:
                self.s3.create_bucket(
                    Bucket=bucket_name,
                    CreateBucketConfiguration={'LocationConstraint': self.region}
                )
            logger.info(f"Created S3 bucket: {bucket_name}")
        
        return bucket_name
    
    def _create_sns_topic(self) -> str:
        """Create test SNS topic"""
        topic_name = f'dyn-opt-notifications-{self.test_suffix}'
        
        response = self.sns.create_topic(Name=topic_name)
        topic_arn = response['TopicArn']
        logger.info(f"Created SNS topic: {topic_arn}")
        
        return topic_arn
    
    def _create_sqs_queues(self) -> Dict[str, str]:
        """Create test SQS queues"""
        queues = {
            'calibration_queue': f'dyn-opt-calibration-{self.test_suffix}',
            'evaluation_queue': f'dyn-opt-evaluation-{self.test_suffix}'
        }
        
        created_queues = {}
        for queue_key, queue_name in queues.items():
            response = self.sqs.create_queue(QueueName=queue_name)
            created_queues[queue_key] = response['QueueUrl']
            logger.info(f"Created SQS queue: {queue_name}")
        
        return created_queues
    
    def cleanup_test_infrastructure(self):
        """Clean up all test resources"""
        # Delete DynamoDB tables
        tables = [
            f'dyn-opt-calibrated-potentials-{self.test_suffix}',
            f'dyn-opt-idp-window-{self.test_suffix}',
            f'dyn-opt-idp-count-{self.test_suffix}',
            f'dyn-opt-decision-change-{self.test_suffix}',
            f'dyn-opt-available-capacity-{self.test_suffix}',
            f'dyn-opt-inc-solds-{self.test_suffix}',
        ]
        
        for table_name in tables:
            try:
                self.dynamodb.delete_table(TableName=table_name)
                logger.info(f"Deleted DynamoDB table: {table_name}")
            except Exception as e:
                logger.warning(f"Failed to delete table {table_name}: {e}")
        
        # Delete S3 bucket (empty it first)
        bucket_name = f'dyn-opt-data-{self.test_suffix}'
        try:
            # Empty bucket
            response = self.s3.list_objects_v2(Bucket=bucket_name)
            if 'Contents' in response:
                objects = [{'Key': obj['Key']} for obj in response['Contents']]
                self.s3.delete_objects(Bucket=bucket_name, Delete={'Objects': objects})
            
            # Delete bucket
            self.s3.delete_bucket(Bucket=bucket_name)
            logger.info(f"Deleted S3 bucket: {bucket_name}")
        except Exception as e:
            logger.warning(f"Failed to delete S3 bucket {bucket_name}: {e}")
        
        # Delete SNS topic
        try:
            topic_name = f'dyn-opt-notifications-{self.test_suffix}'
            topics = self.sns.list_topics()['Topics']
            for topic in topics:
                if topic_name in topic['TopicArn']:
                    self.sns.delete_topic(TopicArn=topic['TopicArn'])
                    logger.info(f"Deleted SNS topic: {topic['TopicArn']}")
        except Exception as e:
            logger.warning(f"Failed to delete SNS topic: {e}")

if __name__ == '__main__':
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Create test infrastructure
    infra = AWSTestInfrastructure()
    resources = infra.create_test_infrastructure()
    
    print("Test infrastructure created:")
    for key, value in resources.items():
        print(f"  {key}: {value}")
