#!/usr/bin/env python3
"""
Integration Test Configuration and Fixtures
Provides shared fixtures and configuration for integration testing
"""

import pytest
import os
import boto3
import logging
import asyncio
from typing import Dict, Any
import requests
import time
from pathlib import Path

from tests.integration.setup.aws_test_infrastructure import AWSTestInfrastructure
from tests.integration.fixtures.calibration_test_data_factory import CalibrationTestDataFactory

# Configure logging for tests
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@pytest.fixture(scope="session")
def test_environment_config():
    """Test environment configuration"""
    return {
        'aws_region': os.getenv('AWS_REGION', 'us-east-2'),
        'test_suffix': f"test-{int(time.time())}",  # Unique suffix for each test run
        'g3_api_mock_url': os.getenv('G3_API_BASE_URL', 'http://localhost:8080'),
        'dynopt_service_url': os.getenv('DYNOPT_SERVICE_URL', 'http://localhost:8001'),
        's3_bucket_prefix': 'dyn-opt-data',
        'table_prefix': 'dyn-opt'
    }

@pytest.fixture(scope="session")
def aws_test_infrastructure(test_environment_config):
    """Set up AWS test infrastructure for the entire test session"""
    logger.info("Setting up AWS test infrastructure...")
    
    infra = AWSTestInfrastructure(
        region=test_environment_config['aws_region'],
        test_suffix=test_environment_config['test_suffix']
    )
    
    try:
        resources = infra.create_test_infrastructure()
        logger.info(f"AWS test infrastructure created: {resources}")
        
        # Set environment variables for the test session
        os.environ.update({
            'ENV': 'test',
            'AWS_REGION': test_environment_config['aws_region'],
            'S3_DATA_BUCKET_NAME': resources['s3_bucket'],
            'CALIBRATED_POTENTIAL_TABLE_NAME': resources['calibrated_potentials'],
            'IDP_WINDOW_TABLE': resources['idp_window'],
            'IDP_COUNT_TABLE': resources['idp_count'],
            'DECISION_CHANGE_TABLE': resources['decision_change'],
            'AVAILABLE_CAPACITY_TABLE': resources['available_capacity'],
            'INC_SOLDS_TABLE_NAME': resources['inc_solds'],
            'FDS_UEN_ARN': resources['sns_topic_arn']
        })
        
        yield resources
        
    finally:
        logger.info("Cleaning up AWS test infrastructure...")
        infra.cleanup_test_infrastructure()

@pytest.fixture(scope="session")
def g3_api_mock_service(test_environment_config):
    """Ensure G3 API mock service is running and accessible"""
    mock_url = test_environment_config['g3_api_mock_url']
    
    # Wait for G3 API mock to be ready
    max_retries = 30
    for attempt in range(max_retries):
        try:
            response = requests.get(f"{mock_url}/health", timeout=5)
            if response.status_code == 200:
                logger.info(f"G3 API mock service is ready at {mock_url}")
                break
        except requests.exceptions.RequestException:
            if attempt < max_retries - 1:
                logger.info(f"Waiting for G3 API mock service... (attempt {attempt + 1})")
                time.sleep(2)
            else:
                pytest.fail(f"G3 API mock service not available at {mock_url}")
    
    yield mock_url

@pytest.fixture(scope="session")
def dynopt_service(test_environment_config):
    """Ensure Dynamic Optimization Service is running and accessible"""
    service_url = test_environment_config['dynopt_service_url']
    
    # Wait for service to be ready
    max_retries = 30
    for attempt in range(max_retries):
        try:
            response = requests.get(f"{service_url}/health-check", timeout=5)
            if response.status_code == 200:
                logger.info(f"Dynamic Optimization Service is ready at {service_url}")
                break
        except requests.exceptions.RequestException:
            if attempt < max_retries - 1:
                logger.info(f"Waiting for Dynamic Optimization Service... (attempt {attempt + 1})")
                time.sleep(2)
            else:
                pytest.fail(f"Dynamic Optimization Service not available at {service_url}")
    
    yield service_url

@pytest.fixture
def test_data_factory():
    """Create test data factory for generating calibration test data"""
    return CalibrationTestDataFactory()

@pytest.fixture
def aws_clients(test_environment_config):
    """Create AWS service clients for testing"""
    region = test_environment_config['aws_region']
    
    return {
        'dynamodb': boto3.client('dynamodb', region_name=region),
        's3': boto3.client('s3', region_name=region),
        'sns': boto3.client('sns', region_name=region),
        'sqs': boto3.client('sqs', region_name=region)
    }

@pytest.fixture
def clean_test_data(aws_test_infrastructure, aws_clients):
    """Clean test data before and after each test"""
    
    def cleanup():
        """Clean up test data from AWS services"""
        try:
            # Clean DynamoDB tables
            for table_key, table_name in aws_test_infrastructure.items():
                if table_key.endswith('_table') or 'table' in table_key:
                    try:
                        # Scan and delete all items
                        response = aws_clients['dynamodb'].scan(TableName=table_name)
                        items = response.get('Items', [])
                        
                        for item in items:
                            # Extract key attributes for deletion
                            key = {}
                            if 'clientCode_propertyCode' in item:
                                key['clientCode_propertyCode'] = item['clientCode_propertyCode']
                            if 'accomClassId' in item:
                                key['accomClassId'] = item['accomClassId']
                            if 'evaluationTime' in item:
                                key['evaluationTime'] = item['evaluationTime']
                            if 'caughtUpDate' in item:
                                key['caughtUpDate'] = item['caughtUpDate']
                            
                            if key:
                                aws_clients['dynamodb'].delete_item(
                                    TableName=table_name,
                                    Key=key
                                )
                        
                        logger.info(f"Cleaned {len(items)} items from {table_name}")
                        
                    except Exception as e:
                        logger.warning(f"Failed to clean table {table_name}: {e}")
            
            # Clean S3 bucket
            bucket_name = aws_test_infrastructure['s3_bucket']
            try:
                response = aws_clients['s3'].list_objects_v2(Bucket=bucket_name)
                objects = response.get('Contents', [])
                
                if objects:
                    delete_objects = [{'Key': obj['Key']} for obj in objects]
                    aws_clients['s3'].delete_objects(
                        Bucket=bucket_name,
                        Delete={'Objects': delete_objects}
                    )
                    logger.info(f"Cleaned {len(objects)} objects from S3 bucket {bucket_name}")
                
            except Exception as e:
                logger.warning(f"Failed to clean S3 bucket {bucket_name}: {e}")
        
        except Exception as e:
            logger.error(f"Error during test data cleanup: {e}")
    
    # Clean before test
    cleanup()
    
    yield
    
    # Clean after test
    cleanup()

@pytest.fixture
def g3_mock_controller(g3_api_mock_service):
    """Controller for managing G3 API mock scenarios"""
    
    class G3MockController:
        def __init__(self, base_url: str):
            self.base_url = base_url
        
        def set_scenario(self, scenario_name: str):
            """Set the current test scenario"""
            response = requests.post(f"{self.base_url}/test/scenario/{scenario_name}")
            if response.status_code != 200:
                raise Exception(f"Failed to set G3 mock scenario: {response.text}")
            logger.info(f"Set G3 mock scenario to: {scenario_name}")
        
        def get_current_scenario(self):
            """Get the current test scenario"""
            response = requests.get(f"{self.base_url}/test/scenario")
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(f"Failed to get G3 mock scenario: {response.text}")
        
        def reset_to_standard(self):
            """Reset to standard test scenario"""
            self.set_scenario('standard')
    
    controller = G3MockController(g3_api_mock_service)
    
    # Reset to standard scenario before each test
    controller.reset_to_standard()
    
    yield controller
    
    # Reset to standard scenario after each test
    controller.reset_to_standard()

@pytest.fixture
def test_isolation_marker(request):
    """Mark test isolation boundaries"""
    test_name = request.node.name
    logger.info(f"=== Starting test: {test_name} ===")
    
    yield test_name
    
    logger.info(f"=== Completed test: {test_name} ===")

# Pytest configuration
def pytest_configure(config):
    """Configure pytest for integration testing"""
    # Add custom markers
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "aws: mark test as requiring AWS services"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )

def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers automatically"""
    for item in items:
        # Add integration marker to all tests in integration directory
        if "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)
        
        # Add aws marker to tests that use AWS fixtures
        if any(fixture in item.fixturenames for fixture in ['aws_test_infrastructure', 'aws_clients']):
            item.add_marker(pytest.mark.aws)
        
        # Add slow marker to workflow tests
        if "workflow" in str(item.fspath):
            item.add_marker(pytest.mark.slow)

# Session-level error handling
@pytest.fixture(scope="session", autouse=True)
def session_error_handler():
    """Handle session-level errors and cleanup"""
    try:
        yield
    except Exception as e:
        logger.error(f"Session-level error occurred: {e}")
        # Perform emergency cleanup if needed
        raise
    finally:
        logger.info("Integration test session completed")
