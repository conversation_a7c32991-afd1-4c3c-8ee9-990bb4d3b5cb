"""
Integration test configuration and shared fixtures.

Provides:
- Test environment setup
- Shared fixtures for integration tests
- Mock service configurations
- Test data management utilities
"""

import pytest
import os
import tempfile
import pandas as pd
from unittest.mock import Mock, patch
from datetime import datetime, date, timedelta
from typing import Dict, Any

# Import test fixtures
from tests.fixtures.g3_api_fixtures import G3ApiTestFixtures


class IntegrationTestConfig:
    """Configuration for integration tests."""
    
    # Test environment variables
    TEST_ENV_VARS = {
        'LOG_LEVEL': 'DEBUG',
        'ENV': 'test',
        'AWS_DEFAULT_REGION': 'us-east-1',
        'S3_DATA_BUCKET_NAME': 'test-dyn-opt-data',
        'CALIBRATED_POTENTIAL_TABLE': 'test-dyn-opt-calibrated-potentials',
        'IDP_WINDOW_TABLE': 'test-dyn-opt-idp-window',
        'IDP_COUNT_TABLE': 'test-dyn-opt-idp-count',
        'DECISION_CHANGE_TABLE': 'test-dyn-opt-decision-change',
        'AVAILABLE_CAPACITY_TABLE': 'test-dyn-opt-available-capacity',
        'G3_AUTH_TOKEN': 'test_token_123',
        'UCS_BASE_URL': 'https://test-ucs.example.com',
        'UPS_BASE_URL': 'https://test-ups.example.com',
        'SECRET_MANAGER_NAME': 'test-secret'
    }


@pytest.fixture(scope='session', autouse=True)
def setup_test_environment():
    """Set up test environment variables."""
    original_env = {}
    
    # Store original values and set test values
    for key, value in IntegrationTestConfig.TEST_ENV_VARS.items():
        original_env[key] = os.environ.get(key)
        os.environ[key] = value
    
    yield
    
    # Restore original values
    for key, original_value in original_env.items():
        if original_value is None:
            os.environ.pop(key, None)
        else:
            os.environ[key] = original_value


@pytest.fixture
def mock_dynamodb_service():
    """Mock DynamoDB service for integration tests."""
    mock_service = Mock()
    
    # Mock calibrated potentials operations
    mock_service.save_calibrated_potentials.return_value = None
    mock_service.fetch_calibrated_potentials.return_value = []
    
    # Mock IDP operations
    mock_service.fetch_idp_count.return_value = pd.DataFrame({'count': [0]})
    mock_service.fetch_inc_solds.return_value = pd.DataFrame({
        'accomClassId': [1, 2],
        'occupancyDate': ['2024-01-15', '2024-01-16'],
        'deltaSolds': [2, 3],
        'captureDate': ['2024-01-08', '2024-01-09']
    })
    
    return mock_service


@pytest.fixture
def mock_s3_service():
    """Mock S3 service for integration tests."""
    mock_service = Mock()
    
    # Mock file operations
    mock_service.fetch_file.return_value = pd.DataFrame({
        'accomClassId': [1, 2],
        'occupancyDate': ['2024-01-15', '2024-01-16'],
        'deltaSolds': [5, 3],
        'leadTime': [7, 14],
        'captureDate': ['2024-01-08', '2024-01-02'],
        'availableCapacity': [15, 10],
        'price': [120.0, 130.0],
        'lrv': [150.0, 160.0],
        'deltaLRV': [5.0, 8.0]
    })
    
    mock_service.save_file.return_value = None
    mock_service.list_objects.return_value = [
        's3://test-bucket/2024-01-15/assemblage.csv',
        's3://test-bucket/2024-01-16/assemblage.csv'
    ]
    mock_service.get_file_name.return_value = '2024-01-15'
    mock_service.get_parent.return_value = 's3://test-bucket/2024-01-15'
    
    return mock_service


@pytest.fixture
def mock_sns_service():
    """Mock SNS service for integration tests."""
    mock_service = Mock()
    mock_service.publish.return_value = {'MessageId': 'test-message-id'}
    return mock_service


@pytest.fixture
def mock_ucs_service():
    """Mock UCS service for integration tests."""
    mock_service = Mock()
    
    # Default configuration values
    mock_service.get_config_param_value.side_effect = lambda param, client, prop: {
        'calibrationRollingWindow': 21,
        'calibrationPercentile': 75,
        'calibrationUseLeadingWindow': False,
        'failOnMissingOccupancyDates': True,
        'pressureFloor': 0.001,
        'wasteThreshold': 2.0,
        'regretThreshold': 1.0,
        'simulationType': 'simplified',
        'currentTargetAvgOpts': 2.5,
        'tolerableBinarySearchError': 0.1,
        'minShiftDeltaLrvThreshold': 0.015,
        'maxShiftLrvThreshold': 0.10
    }.get(param, 'default_value')
    
    return mock_service


@pytest.fixture
def mock_ups_service():
    """Mock UPS service for integration tests."""
    mock_service = Mock()
    
    # Mock property info
    from src.common.dto.property_info import PropertyInfo
    mock_property_info = PropertyInfo(
        client_code='Hilton',
        property_code='TPANH',
        time_zone='America/New_York',
        tier='Tier1',
        brand_code='HI',
        global_area='Americas',
        location_type='Urban',
        g3_host_url='https://g3.example.com'
    )
    
    mock_service.get_param.return_value = mock_property_info
    
    return mock_service


@pytest.fixture
def sample_calibration_data():
    """Sample calibration data for testing."""
    return {
        'delta_occ_solds': pd.DataFrame({
            'accomClassId': [1, 1, 2, 2],
            'occupancyDate': ['2024-01-15', '2024-01-16', '2024-01-15', '2024-01-16'],
            'deltaSolds': [2, 3, 1, 2],
            'leadTime': [7, 6, 7, 6],
            'captureDate': ['2024-01-08', '2024-01-10', '2024-01-08', '2024-01-10'],
            'availableCapacity': [15, 12, 10, 8]
        }),
        'reference_rate': pd.DataFrame({
            'accomClassId': [1, 1, 2, 2],
            'occupancyDate': ['2024-01-15', '2024-01-16', '2024-01-15', '2024-01-16'],
            'price': [120.0, 130.0, 110.0, 125.0],
            'leadTime': [7, 6, 7, 6],
            'captureDate': ['2024-01-08', '2024-01-10', '2024-01-08', '2024-01-10']
        }),
        'delta_lrv': pd.DataFrame({
            'accomClassId': [1, 1, 2, 2],
            'occupancyDate': ['2024-01-15', '2024-01-16', '2024-01-15', '2024-01-16'],
            'deltaLRV': [5.0, 8.0, 3.0, 7.0],
            'leadTime': [7, 6, 7, 6],
            'captureDate': ['2024-01-08', '2024-01-10', '2024-01-08', '2024-01-10']
        })
    }


@pytest.fixture
def sample_assemblage_data():
    """Sample assemblage data for RevisionProcess testing."""
    return pd.DataFrame({
        'accomClassId': [1, 1, 2, 2],
        'occupancyDate': ['2024-01-15', '2024-01-16', '2024-01-15', '2024-01-16'],
        'evaluationTime': ['2024-01-08', '2024-01-09', '2024-01-08', '2024-01-09'],
        'pressure': [0.5, 0.7, 0.3, 0.6],
        'potential': [10.5, 12.3, 8.7, 9.2],
        'lrv': [150.0, 160.0, 140.0, 155.0],
        'price': [120.0, 130.0, 110.0, 125.0],
        'deltaLRV': [5.0, 8.0, 3.0, 7.0],
        'calibratedPotential': [50.0, 75.0, 50.0, 75.0],
        'incSolds': [2, 3, 1, 2],
        'caughtUpDate': ['2024-01-15', '2024-01-16', '2024-01-15', '2024-01-16'],
        'leadTime': [7, 6, 7, 6],
        'availableCapacity': [15, 12, 10, 8],
        'deltaSolds': [2, 3, 1, 2],
        'ceilingValue': [200.0, 210.0, 190.0, 205.0]
    })


@pytest.fixture
def sample_quantile_functions():
    """Sample quantile functions for testing."""
    return {
        1: lambda x: x * 0.8,  # Simple linear function for accom class 1
        2: lambda x: x * 0.9   # Simple linear function for accom class 2
    }


@pytest.fixture
def temp_file_manager():
    """Temporary file manager for tests."""
    temp_files = []
    
    def create_temp_file(content: str = "", suffix: str = ".csv") -> str:
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix=suffix, delete=False)
        temp_file.write(content)
        temp_file.close()
        temp_files.append(temp_file.name)
        return temp_file.name
    
    yield create_temp_file
    
    # Cleanup
    for temp_file in temp_files:
        try:
            os.unlink(temp_file)
        except FileNotFoundError:
            pass


@pytest.fixture
def integration_test_context():
    """Comprehensive integration test context with all mocked services."""
    context = {
        'client_code': 'Hilton',
        'property_code': 'TPANH',
        'test_date': date(2024, 1, 15),
        'test_datetime': datetime(2024, 1, 15, 10, 30, 0)
    }
    
    return context


# Include G3 API fixtures
g3_fixtures = G3ApiTestFixtures()
for attr_name in dir(g3_fixtures):
    if attr_name.startswith('_') or not callable(getattr(g3_fixtures, attr_name)):
        continue
    
    # Get the fixture function
    fixture_func = getattr(g3_fixtures, attr_name)
    if hasattr(fixture_func, '_pytestfixturefunction'):
        # Add to global scope so pytest can find it
        globals()[attr_name] = fixture_func
