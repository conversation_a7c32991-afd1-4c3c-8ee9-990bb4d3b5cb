#!/usr/bin/env python3
"""
End-to-End Calibration Workflow Integration Test
Tests the complete calibration workflow using real AWS services and mocked G3 API
"""

import pytest
import asyncio
import boto3
import pandas as pd
import requests
import time
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any
import logging
import os
from pathlib import Path

from tests.integration.fixtures.calibration_test_data_factory import CalibrationTestDataFactory
from tests.integration.setup.aws_test_infrastructure import AWSTestInfrastructure
from src.common.dto.calib_request import CalibrationRequestSource
from src.common.dto.calibration_s3_file_input import CalibrationS3FileInput
from src.common.calibration_service import CalibrationService
from src.common.dynamodb_service import DynamoDBService

logger = logging.getLogger(__name__)

class TestCalibrationWorkflow:
    """Integration tests for the complete calibration workflow"""
    
    @pytest.fixture(scope="class")
    def aws_infrastructure(self):
        """Set up AWS test infrastructure"""
        infra = AWSTestInfrastructure(test_suffix='integration-test')
        resources = infra.create_test_infrastructure()
        
        yield resources
        
        # Cleanup after tests
        infra.cleanup_test_infrastructure()
    
    @pytest.fixture(scope="class")
    def test_data_factory(self):
        """Create test data factory"""
        return CalibrationTestDataFactory()
    
    @pytest.fixture(scope="class")
    def g3_api_mock_url(self):
        """G3 API mock service URL"""
        return os.getenv('G3_API_BASE_URL', 'http://localhost:8080')
    
    @pytest.fixture(scope="class")
    def dynopt_service_url(self):
        """Dynamic Optimization Service URL"""
        return os.getenv('DYNOPT_SERVICE_URL', 'http://localhost:8001')
    
    @pytest.fixture
    def calibration_service(self, aws_infrastructure):
        """Create calibration service with test configuration"""
        # Set environment variables for test
        os.environ['CALIBRATED_POTENTIAL_TABLE_NAME'] = aws_infrastructure['calibrated_potentials']
        os.environ['S3_DATA_BUCKET_NAME'] = aws_infrastructure['s3_bucket']
        
        return CalibrationService()
    
    @pytest.fixture
    def dynamodb_service(self, aws_infrastructure):
        """Create DynamoDB service for test validation"""
        return DynamoDBService()
    
    def setup_g3_mock_scenario(self, g3_api_mock_url: str, scenario: str):
        """Set up G3 API mock with specific test scenario"""
        response = requests.post(f"{g3_api_mock_url}/test/scenario/{scenario}")
        assert response.status_code == 200, f"Failed to set G3 mock scenario: {response.text}"
        logger.info(f"Set G3 mock scenario to: {scenario}")
    
    def upload_test_data_to_s3(self, test_data_factory: CalibrationTestDataFactory,
                              scenario_name: str, s3_bucket: str) -> Dict[str, str]:
        """Upload test data to S3 and return S3 paths"""
        # Generate test scenario data
        scenario_data = test_data_factory.create_calibration_scenario(scenario_name)
        
        # Upload to S3
        client_code = 'TestClient'
        property_code = 'TEST01'
        calibration_date = datetime.now().strftime('%Y-%m-%d')
        
        s3_paths = test_data_factory.save_scenario_to_s3(
            scenario_data, s3_bucket, client_code, property_code, calibration_date
        )
        
        logger.info(f"Uploaded {scenario_name} test data to S3: {s3_paths}")
        return s3_paths
    
    def wait_for_calibration_completion(self, dynamodb_service: DynamoDBService,
                                      client_code: str, property_code: str,
                                      timeout: int = 300) -> bool:
        """Wait for calibration to complete by checking DynamoDB"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # Check if calibrated potentials exist
                potentials = dynamodb_service.fetch_calibrated_potentials(
                    'dyn-opt-calibrated-potentials-integration-test',
                    client_code,
                    property_code
                )
                
                if potentials and len(potentials) > 0:
                    logger.info(f"Calibration completed. Found {len(potentials)} calibrated potentials")
                    return True
                    
            except Exception as e:
                logger.debug(f"Calibration not yet complete: {e}")
            
            time.sleep(10)  # Check every 10 seconds
        
        logger.error(f"Calibration did not complete within {timeout} seconds")
        return False
    
    @pytest.mark.asyncio
    async def test_standard_calibration_workflow(self, aws_infrastructure, test_data_factory,
                                                g3_api_mock_url, calibration_service,
                                                dynamodb_service):
        """Test standard calibration workflow end-to-end"""
        
        # Step 1: Set up G3 API mock with standard scenario
        self.setup_g3_mock_scenario(g3_api_mock_url, 'standard')
        
        # Step 2: Upload test data to S3
        s3_paths = self.upload_test_data_to_s3(
            test_data_factory, 'standard', aws_infrastructure['s3_bucket']
        )
        
        # Step 3: Create calibration request
        calibration_request = CalibrationRequestSource(
            client_code='TestClient',
            property_code='TEST01',
            destination_table=aws_infrastructure['calibrated_potentials'],
            request_context=CalibrationS3FileInput(
                delta_occ_solds=s3_paths['delta_occ_solds'],
                reference_rate=s3_paths['reference_rate'],
                delta_lrv=s3_paths['delta_lrv'],
                min_data_requirement=7,
                max_data_limit=30
            )
        )
        
        # Step 4: Execute calibration
        logger.info("Starting calibration workflow...")
        start_time = time.time()
        
        try:
            calibration_service.run_full_calibration_from_s3_save_results(calibration_request)
            execution_time = time.time() - start_time
            logger.info(f"Calibration completed in {execution_time:.2f} seconds")
            
        except Exception as e:
            logger.error(f"Calibration failed: {e}")
            raise
        
        # Step 5: Validate results in DynamoDB
        assert self.wait_for_calibration_completion(
            dynamodb_service, 'TestClient', 'TEST01'
        ), "Calibration did not complete successfully"
        
        # Step 6: Verify calibrated potentials
        potentials = dynamodb_service.fetch_calibrated_potentials(
            aws_infrastructure['calibrated_potentials'],
            'TestClient',
            'TEST01'
        )
        
        assert len(potentials) > 0, "No calibrated potentials found"
        
        # Validate potential values are reasonable
        for potential in potentials:
            assert potential.calibrated_potential > 0, f"Invalid potential value: {potential.calibrated_potential}"
            assert potential.accom_class_id in [1, 2, 3, 4, 5], f"Invalid accommodation class: {potential.accom_class_id}"
        
        logger.info(f"Successfully validated {len(potentials)} calibrated potentials")
    
    @pytest.mark.asyncio
    async def test_high_demand_calibration_workflow(self, aws_infrastructure, test_data_factory,
                                                   g3_api_mock_url, calibration_service,
                                                   dynamodb_service):
        """Test calibration workflow with high demand scenario"""
        
        # Set up high demand scenario
        self.setup_g3_mock_scenario(g3_api_mock_url, 'high_volume')
        
        # Upload high demand test data
        s3_paths = self.upload_test_data_to_s3(
            test_data_factory, 'high_demand', aws_infrastructure['s3_bucket']
        )
        
        # Create calibration request
        calibration_request = CalibrationRequestSource(
            client_code='TestClient',
            property_code='TEST02',  # Different property for isolation
            destination_table=aws_infrastructure['calibrated_potentials'],
            request_context=CalibrationS3FileInput(
                delta_occ_solds=s3_paths['delta_occ_solds'],
                reference_rate=s3_paths['reference_rate'],
                delta_lrv=s3_paths['delta_lrv'],
                min_data_requirement=7,
                max_data_limit=30
            )
        )
        
        # Execute calibration
        logger.info("Starting high demand calibration workflow...")
        calibration_service.run_full_calibration_from_s3_save_results(calibration_request)
        
        # Validate results
        assert self.wait_for_calibration_completion(
            dynamodb_service, 'TestClient', 'TEST02'
        ), "High demand calibration did not complete"
        
        potentials = dynamodb_service.fetch_calibrated_potentials(
            aws_infrastructure['calibrated_potentials'],
            'TestClient',
            'TEST02'
        )
        
        assert len(potentials) > 0, "No calibrated potentials found for high demand scenario"
        
        # High demand should result in higher potential values
        avg_potential = sum(p.calibrated_potential for p in potentials) / len(potentials)
        assert avg_potential > 0.5, f"Expected higher potentials for high demand, got {avg_potential}"
        
        logger.info(f"High demand calibration completed with average potential: {avg_potential}")
    
    @pytest.mark.asyncio
    async def test_revision_process_integration(self, aws_infrastructure, test_data_factory,
                                              g3_api_mock_url, calibration_service):
        """Test RevisionProcess integration within calibration workflow"""
        
        # Set up standard scenario
        self.setup_g3_mock_scenario(g3_api_mock_url, 'standard')
        
        # Upload test data
        s3_paths = self.upload_test_data_to_s3(
            test_data_factory, 'volatile_pricing', aws_infrastructure['s3_bucket']
        )
        
        # Create calibration request with revision process parameters
        calibration_request = CalibrationRequestSource(
            client_code='TestClient',
            property_code='TEST03',
            destination_table=aws_infrastructure['calibrated_potentials'],
            request_context=CalibrationS3FileInput(
                delta_occ_solds=s3_paths['delta_occ_solds'],
                reference_rate=s3_paths['reference_rate'],
                delta_lrv=s3_paths['delta_lrv'],
                min_data_requirement=7,
                max_data_limit=30
            )
        )
        
        # Execute calibration with revision process
        logger.info("Starting calibration with revision process...")
        start_time = time.time()
        
        calibration_service.run_full_calibration_from_s3_save_results(calibration_request)
        
        execution_time = time.time() - start_time
        logger.info(f"Calibration with revision process completed in {execution_time:.2f} seconds")
        
        # Validate that revision process was executed
        # (This would be validated through logs or additional metrics in a real implementation)
        assert execution_time > 5, "Revision process should take some time to execute"
        assert execution_time < 120, "Revision process should not take too long"
    
    @pytest.mark.asyncio
    async def test_error_handling_workflow(self, aws_infrastructure, test_data_factory,
                                         g3_api_mock_url, calibration_service):
        """Test error handling in calibration workflow"""
        
        # Set up error scenario in G3 mock
        self.setup_g3_mock_scenario(g3_api_mock_url, 'error_scenario')
        
        # Upload minimal test data
        s3_paths = self.upload_test_data_to_s3(
            test_data_factory, 'edge_case', aws_infrastructure['s3_bucket']
        )
        
        # Create calibration request
        calibration_request = CalibrationRequestSource(
            client_code='TestClient',
            property_code='TEST04',
            destination_table=aws_infrastructure['calibrated_potentials'],
            request_context=CalibrationS3FileInput(
                delta_occ_solds=s3_paths['delta_occ_solds'],
                reference_rate=s3_paths['reference_rate'],
                delta_lrv=s3_paths['delta_lrv'],
                min_data_requirement=7,
                max_data_limit=30
            )
        )
        
        # Execute calibration and expect it to handle errors gracefully
        logger.info("Testing error handling in calibration workflow...")
        
        with pytest.raises(Exception):
            calibration_service.run_full_calibration_from_s3_save_results(calibration_request)
        
        logger.info("Error handling test completed successfully")
    
    def test_aws_services_connectivity(self, aws_infrastructure):
        """Test connectivity to real AWS services"""
        
        # Test DynamoDB connectivity
        dynamodb = boto3.client('dynamodb')
        response = dynamodb.describe_table(TableName=aws_infrastructure['calibrated_potentials'])
        assert response['Table']['TableStatus'] == 'ACTIVE'
        
        # Test S3 connectivity
        s3 = boto3.client('s3')
        response = s3.head_bucket(Bucket=aws_infrastructure['s3_bucket'])
        assert response['ResponseMetadata']['HTTPStatusCode'] == 200
        
        # Test SNS connectivity
        sns = boto3.client('sns')
        response = sns.get_topic_attributes(TopicArn=aws_infrastructure['sns_topic_arn'])
        assert 'Attributes' in response
        
        logger.info("All AWS services connectivity tests passed")

if __name__ == '__main__':
    # Run tests
    pytest.main([__file__, '-v', '--tb=short'])
