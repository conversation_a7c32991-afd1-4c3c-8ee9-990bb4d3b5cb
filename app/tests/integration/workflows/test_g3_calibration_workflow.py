"""
End-to-end integration tests for G3 API → Calibration → RevisionProcess workflow.

Tests the complete flow:
1. G3 API data fetching (InputProcessingRequest)
2. CalibrationService processing
3. RevisionProcess execution
4. DynamoDB persistence
5. Error propagation throughout the pipeline
"""

import pytest
import pandas as pd
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, date, timed<PERSON>ta
from requests import HTTPError, Timeout

from src.common.calibration_service import CalibrationService
from src.common.dto.calib_request import CalibrationRequestSource
from src.common.dto.calibration_s3_file_input import CalibrationS3FileInput
from src.common.g3_any_api_service import G3AnyApiGlobalService, InputProcessingRequest
from src.common.http_service import HttpService
from src.common.http_authorizers.g3_auth import G3Auth
from src.common.ups_service import get_property_host_url
from src.algorithm.dynamic_optimization_package.RevisionPackage.RevisionProcess import RevisionProcess


class TestG3CalibrationWorkflowIntegration:
    """Test end-to-end G3 API to calibration workflow integration."""

    @pytest.fixture
    def mock_dynamodb_service(self):
        """Mock DynamoDB service for testing."""
        mock_service = Mock()
        mock_service.save_calibrated_potentials.return_value = None
        mock_service.fetch_calibrated_potentials.return_value = []
        return mock_service

    @pytest.fixture
    def mock_s3_service(self):
        """Mock S3 service for testing."""
        mock_service = Mock()
        # Mock S3 file reading for calibration data
        mock_service.fetch_file.return_value = pd.DataFrame({
            'accomClassId': [1, 2],
            'occupancyDate': ['2024-01-15', '2024-01-16'],
            'deltaSolds': [5, 3],
            'leadTime': [7, 14],
            'captureDate': ['2024-01-08', '2024-01-02'],
            'availableCapacity': [15, 10]
        })
        return mock_service

    @pytest.fixture
    def sample_g3_input_processing_data(self):
        """Sample G3 input processing data."""
        return pd.DataFrame({
            'clientCode': ['Hilton', 'Hilton', 'Hilton'],
            'propertyCode': ['TPANH', 'TPANH', 'TPANH'],
            'processingType': ['CDP', 'BDE', 'CDP_DYNAMIC'],
            'preparedDate': [
                '2024-01-15 10:30:00',
                '2024-01-15 11:45:00', 
                '2024-01-15 12:15:00'
            ],
            'timeZone': ['America/New_York', 'America/New_York', 'America/New_York']
        })

    @pytest.fixture
    def sample_assemblage_data(self):
        """Sample assemblage data for RevisionProcess."""
        return pd.DataFrame({
            'accomClassId': [1, 1, 2, 2],
            'occupancyDate': ['2024-01-15', '2024-01-16', '2024-01-15', '2024-01-16'],
            'evaluationTime': ['2024-01-08', '2024-01-09', '2024-01-08', '2024-01-09'],
            'pressure': [0.5, 0.7, 0.3, 0.6],
            'potential': [10.5, 12.3, 8.7, 9.2],
            'lrv': [150.0, 160.0, 140.0, 155.0],
            'price': [120.0, 130.0, 110.0, 125.0],
            'deltaLRV': [5.0, 8.0, 3.0, 7.0],
            'calibratedPotential': [50.0, 75.0, 50.0, 75.0],
            'incSolds': [2, 3, 1, 2],
            'caughtUpDate': ['2024-01-15', '2024-01-16', '2024-01-15', '2024-01-16'],
            'leadTime': [7, 6, 7, 6],
            'availableCapacity': [15, 12, 10, 8],
            'deltaSolds': [2, 3, 1, 2],
            'ceilingValue': [200.0, 210.0, 190.0, 205.0]
        })

    @pytest.fixture
    def mock_quantile_functions(self):
        """Mock quantile functions for RevisionProcess."""
        return {
            1: lambda x: x * 0.8,  # Simple linear function for accom class 1
            2: lambda x: x * 0.9   # Simple linear function for accom class 2
        }

    @pytest.fixture
    def calibration_service(self, mock_dynamodb_service):
        """Create CalibrationService with mocked dependencies."""
        return CalibrationService(_dynamodb_service=mock_dynamodb_service)

    @patch('src.common.ups_service.get_property_host_url')
    @patch('src.common.http_service.HttpService')
    @patch('src.common.g3_any_api_service.G3AnyApiGlobalService')
    def test_successful_g3_to_calibration_workflow(
        self,
        mock_g3_service_class,
        mock_http_service_class,
        mock_get_host_url,
        calibration_service,
        sample_g3_input_processing_data,
        sample_assemblage_data,
        mock_quantile_functions,
        mock_s3_service
    ):
        """Test successful end-to-end workflow from G3 API to calibration completion."""
        # Arrange
        mock_get_host_url.return_value = 'https://g3.example.com'
        
        mock_g3_service = Mock()
        mock_g3_service.fetch.return_value = sample_g3_input_processing_data
        mock_g3_service_class.return_value = mock_g3_service

        mock_http_service = Mock()
        mock_http_service_class.return_value = mock_http_service

        # Mock RevisionProcess components
        with patch.object(calibration_service, 'get_latest_quantile_functions') as mock_qfun:
            mock_qfun.return_value = mock_quantile_functions
            
            with patch('src.common.s3_service.s3_service', mock_s3_service):
                with patch.object(calibration_service.rp, 'run_revision') as mock_revision:
                    # Mock revision process return values
                    mock_revision.return_value = (
                        75.0,  # new_percentile
                        2.5,   # target
                        2.3,   # observed_nopts
                        'increase',  # adjustment_type
                        0.05,  # med_max_shift_lrv
                        0.03   # med_max_shift_delta_lrv
                    )

                    # Create calibration request
                    request = CalibrationRequestSource(
                        client_code='Hilton',
                        property_code='TPANH',
                        destination_table='dyn-opt-calibrated-potentials',
                        request_context=CalibrationS3FileInput(
                            delta_occ_solds='s3://test-bucket/deltaOcc.csv',
                            reference_rate='s3://test-bucket/refRate.csv',
                            delta_lrv='s3://test-bucket/deltaLrv.csv',
                            min_data_requirement=21,
                            max_data_limit=21
                        )
                    )

                    # Act
                    calibration_service.get_calibrated_percentile(
                        'Hilton', 'TPANH', 50.0
                    )

                    # Assert
                    # Verify G3 API was called
                    mock_get_host_url.assert_called_with(client_code='Hilton', property_code='TPANH')
                    mock_http_service_class.assert_called_with(
                        'https://g3.example.com', 
                        authorizer=pytest.any(G3Auth)
                    )
                    mock_g3_service_class.assert_called_with(mock_http_service, 'Hilton')
                    mock_g3_service.fetch.assert_called_once()
                    
                    # Verify the fetch call was made with InputProcessingRequest
                    fetch_call_args = mock_g3_service.fetch.call_args[0][0]
                    assert isinstance(fetch_call_args, InputProcessingRequest)
                    assert fetch_call_args.client_code == 'Hilton'
                    assert fetch_call_args.property_code == 'TPANH'

                    # Verify RevisionProcess was called
                    mock_revision.assert_called_once()
                    revision_call_kwargs = mock_revision.call_args[1]
                    assert 'assem' in revision_call_kwargs
                    assert 'waste_threshold' in revision_call_kwargs
                    assert 'regret_threshold' in revision_call_kwargs
                    assert 'qfun' in revision_call_kwargs
                    assert 'current_calib_percentile' in revision_call_kwargs

    @patch('src.common.ups_service.get_property_host_url')
    @patch('src.common.http_service.HttpService')
    @patch('src.common.g3_any_api_service.G3AnyApiGlobalService')
    def test_g3_api_failure_error_propagation(
        self,
        mock_g3_service_class,
        mock_http_service_class,
        mock_get_host_url,
        calibration_service
    ):
        """Test error propagation when G3 API fails."""
        # Arrange
        mock_get_host_url.return_value = 'https://g3.example.com'
        
        mock_g3_service = Mock()
        mock_g3_service.fetch.side_effect = HTTPError("401 Unauthorized")
        mock_g3_service_class.return_value = mock_g3_service

        mock_http_service = Mock()
        mock_http_service_class.return_value = mock_http_service

        # Act & Assert
        with pytest.raises(HTTPError):
            calibration_service.get_calibrated_percentile('Hilton', 'TPANH', 50.0)

    @patch('src.common.ups_service.get_property_host_url')
    @patch('src.common.http_service.HttpService')
    @patch('src.common.g3_any_api_service.G3AnyApiGlobalService')
    def test_g3_api_timeout_error_propagation(
        self,
        mock_g3_service_class,
        mock_http_service_class,
        mock_get_host_url,
        calibration_service
    ):
        """Test error propagation when G3 API times out."""
        # Arrange
        mock_get_host_url.return_value = 'https://g3.example.com'
        
        mock_g3_service = Mock()
        mock_g3_service.fetch.side_effect = Timeout("Request timed out")
        mock_g3_service_class.return_value = mock_g3_service

        mock_http_service = Mock()
        mock_http_service_class.return_value = mock_http_service

        # Act & Assert
        with pytest.raises(Timeout):
            calibration_service.get_calibrated_percentile('Hilton', 'TPANH', 50.0)

    @patch('src.common.ups_service.get_property_host_url')
    @patch('src.common.http_service.HttpService')
    @patch('src.common.g3_any_api_service.G3AnyApiGlobalService')
    def test_g3_api_empty_data_handling(
        self,
        mock_g3_service_class,
        mock_http_service_class,
        mock_get_host_url,
        calibration_service,
        mock_quantile_functions
    ):
        """Test handling when G3 API returns empty data."""
        # Arrange
        mock_get_host_url.return_value = 'https://g3.example.com'
        
        # Return empty DataFrame
        empty_df = pd.DataFrame(columns=[
            'clientCode', 'propertyCode', 'processingType', 'preparedDate', 'timeZone'
        ])
        
        mock_g3_service = Mock()
        mock_g3_service.fetch.return_value = empty_df
        mock_g3_service_class.return_value = mock_g3_service

        mock_http_service = Mock()
        mock_http_service_class.return_value = mock_http_service

        with patch.object(calibration_service, 'get_latest_quantile_functions') as mock_qfun:
            mock_qfun.return_value = mock_quantile_functions
            
            with patch.object(calibration_service.rp, 'run_revision') as mock_revision:
                # Mock revision process to handle empty data
                mock_revision.side_effect = ValueError("Insufficient data for revision")

                # Act & Assert
                with pytest.raises(ValueError, match="Insufficient data for revision"):
                    calibration_service.get_calibrated_percentile('Hilton', 'TPANH', 50.0)
