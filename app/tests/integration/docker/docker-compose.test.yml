version: '3.8'

services:
  # G3 API Mock Service
  g3-api-mock:
    build:
      context: ../mocks
      dockerfile: Dockerfile.g3-mock
    ports:
      - "8080:8080"
    environment:
      - LOG_LEVEL=DEBUG
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - test-network

  # PostgreSQL for G3 Mock Database (optional)
  g3-mock-db:
    image: postgres:15
    environment:
      POSTGRES_DB: g3_mock
      POSTGRES_USER: g3_user
      POSTGRES_PASSWORD: g3_password
    ports:
      - "5433:5432"
    volumes:
      - g3_mock_data:/var/lib/postgresql/data
      - ../mocks/sql:/docker-entrypoint-initdb.d
    networks:
      - test-network

  # Dynamic Optimization Service (Test Configuration)
  dynopt-service-test:
    build:
      context: ../../../
      dockerfile: dockerfile
      target: runtime
    ports:
      - "8001:8000"
    environment:
      # Test environment configuration
      - ENV=test
      - LOG_LEVEL=DEBUG
      
      # AWS Test Configuration
      - AWS_REGION=us-east-2
      - S3_DATA_BUCKET_NAME=dyn-opt-data-test
      - CALIBRATED_POTENTIAL_TABLE_NAME=dyn-opt-calibrated-potentials-test
      - IDP_WINDOW_TABLE=dyn-opt-idp-window-test
      - IDP_COUNT_TABLE=dyn-opt-idp-count-test
      - DECISION_CHANGE_TABLE=dyn-opt-decision-change-test
      - AVAILABLE_CAPACITY_TABLE=dyn-opt-available-capacity-test
      - INC_SOLDS_TABLE_NAME=dyn-opt-inc-solds-test
      - FDS_UEN_ARN=arn:aws:sns:us-east-2:123456789012:dyn-opt-test-notifications
      
      # Mock G3 API Configuration
      - G3_API_BASE_URL=http://g3-api-mock:8080
      - G3_AUTH_TOKEN=test-token
      
      # UCS/UPS Mock Configuration (point to mock services)
      - UCS_BASE_URL=http://g3-api-mock:8080/mock/ucs
      - UPS_BASE_URL=http://g3-api-mock:8080/mock/ups
      - UIS_AUTH_URL=http://g3-api-mock:8080/mock/uis
      
      # AWS Credentials (use your test credentials)
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN}
      
      # SQS Configuration
      - CALIB_REQ_URL=https://sqs.us-east-2.amazonaws.com/123456789012/dyn-opt-calibration-test
      - EVALUATION_REQ_URL=https://sqs.us-east-2.amazonaws.com/123456789012/dyn-opt-evaluation-test
      
    volumes:
      - test_data:/app/files
      - ../fixtures/sample_data:/app/test_data
    depends_on:
      g3-api-mock:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health-check"]
      interval: 15s
      timeout: 10s
      retries: 5
    networks:
      - test-network

  # Test Runner Container
  test-runner:
    build:
      context: ../../../
      dockerfile: dockerfile
      target: runtime
    environment:
      # Same environment as dynopt-service-test
      - ENV=test
      - LOG_LEVEL=DEBUG
      - AWS_REGION=us-east-2
      - S3_DATA_BUCKET_NAME=dyn-opt-data-test
      - CALIBRATED_POTENTIAL_TABLE_NAME=dyn-opt-calibrated-potentials-test
      - G3_API_BASE_URL=http://g3-api-mock:8080
      - DYNOPT_SERVICE_URL=http://dynopt-service-test:8000
      
      # AWS Credentials
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_SESSION_TOKEN=${AWS_SESSION_TOKEN}
      
    volumes:
      - ../../../:/app
      - test_data:/app/files
      - ../fixtures:/app/test_fixtures
    working_dir: /app
    command: >
      sh -c "
        echo 'Waiting for services to be ready...' &&
        sleep 30 &&
        echo 'Running integration tests...' &&
        poetry run pytest tests/integration/workflows/test_calibration_workflow.py -v --tb=short
      "
    depends_on:
      dynopt-service-test:
        condition: service_healthy
      g3-api-mock:
        condition: service_healthy
    networks:
      - test-network

volumes:
  g3_mock_data:
  test_data:

networks:
  test-network:
    driver: bridge
