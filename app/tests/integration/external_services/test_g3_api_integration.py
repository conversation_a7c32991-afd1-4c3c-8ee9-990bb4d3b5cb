"""
Integration tests for G3 API service.

Tests G3 API integration including:
- Mock G3 API service responses
- Authentication handling
- Error scenarios (timeouts, invalid responses, auth failures)
- Data parsing and validation
"""

import json
import pytest
import pandas as pd
from unittest.mock import Mock, patch, MagicMock
from requests import HTTPError, Timeout, ConnectionError
from datetime import datetime, timedelta

from src.common.g3_any_api_service import (
    G3AnyApiService, 
    G3AnyApiGlobalService,
    InputProcessingRequest,
    PropertyIdRequest,
    PaceAccomActivityRequest
)
from src.common.http_service import HttpService
from src.common.http_authorizers.g3_auth import G3Auth
from src.common.exceptions.http_exception import HttpException


class TestG3ApiServiceIntegration:
    """Test G3 API service integration with mocked responses."""

    @pytest.fixture
    def mock_http_service(self):
        """Create a mock HTTP service for testing."""
        return Mock(spec=HttpService)

    @pytest.fixture
    def mock_g3_auth(self):
        """Create a mock G3 authentication."""
        return Mock(spec=G3Auth)

    @pytest.fixture
    def sample_input_processing_data(self):
        """Sample input processing data from G3 API."""
        return [
            ['Hilton', 'TPANH', 'CDP', '2024-01-15 10:30:00', 'America/New_York'],
            ['Hilton', 'TPANH', 'BDE', '2024-01-15 11:45:00', 'America/New_York'],
            ['Hilton', 'TPANH', 'CDP_DYNAMIC', '2024-01-15 12:15:00', 'America/New_York']
        ]

    @pytest.fixture
    def sample_property_id_data(self):
        """Sample property ID data from G3 API."""
        return [
            ['Hilton', 'TPANH', '12345']
        ]

    @pytest.fixture
    def sample_pace_accom_data(self):
        """Sample pace accommodation activity data from G3 API."""
        return [
            ['2024-01-15', 7, 'STD', 100, 85, 15],
            ['2024-01-15', 14, 'STD', 100, 90, 10],
            ['2024-01-16', 7, 'SUP', 50, 45, 5]
        ]

    def test_g3_any_api_global_service_input_processing_success(
        self, mock_http_service, sample_input_processing_data
    ):
        """Test successful InputProcessingRequest via G3AnyApiGlobalService."""
        # Arrange
        mock_http_service.post.return_value = sample_input_processing_data
        service = G3AnyApiGlobalService(mock_http_service, 'Hilton')
        request = InputProcessingRequest('Hilton', 'TPANH')

        # Act
        result = service.fetch(request)

        # Assert
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 3
        assert list(result.columns) == [
            'clientCode', 'propertyCode', 'processingType', 'preparedDate', 'timeZone'
        ]
        assert result.iloc[0]['clientCode'] == 'Hilton'
        assert result.iloc[0]['propertyCode'] == 'TPANH'
        assert result.iloc[0]['processingType'] == 'CDP'

        # Verify HTTP service was called correctly
        mock_http_service.post.assert_called_once()
        call_args = mock_http_service.post.call_args
        assert call_args[0][0] == 'api/G3Tables/executeQueryOnGlobal/v1'
        assert 'clientCode' in call_args[1]['params']
        assert call_args[1]['params']['clientCode'] == 'Hilton'

    def test_g3_any_api_service_property_id_success(
        self, mock_http_service, sample_property_id_data
    ):
        """Test successful PropertyIdRequest via G3AnyApiGlobalService."""
        # Arrange
        mock_http_service.post.return_value = sample_property_id_data
        service = G3AnyApiGlobalService(mock_http_service, 'Hilton')
        request = PropertyIdRequest('Hilton', 'TPANH')

        # Act
        result = service.fetch(request)

        # Assert
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 1
        assert list(result.columns) == ['clientCode', 'propertyCode', 'propertyId']
        assert result.iloc[0]['propertyId'] == '12345'

    def test_g3_any_api_service_pace_accom_success(
        self, mock_http_service, sample_pace_accom_data
    ):
        """Test successful PaceAccomActivityRequest via G3AnyApiService."""
        # Arrange
        mock_http_service.post.return_value = sample_pace_accom_data
        service = G3AnyApiService(mock_http_service, '12345')
        start_date = datetime(2024, 1, 15)
        end_date = datetime(2024, 1, 16)
        request = PaceAccomActivityRequest(start_date, end_date)

        # Act
        result = service.fetch(request)

        # Assert
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 3
        expected_columns = [
            'occupancyDate', 'leadTime', 'accomClassId', 
            'accomCapacity', 'roomsSold', 'availableCapacity'
        ]
        assert list(result.columns) == expected_columns

    def test_g3_api_authentication_failure(self, mock_http_service):
        """Test G3 API authentication failure handling."""
        # Arrange
        mock_http_service.post.side_effect = HTTPError("401 Unauthorized")
        service = G3AnyApiGlobalService(mock_http_service, 'Hilton')
        request = InputProcessingRequest('Hilton', 'TPANH')

        # Act & Assert
        with pytest.raises(HTTPError):
            service.fetch(request)

    def test_g3_api_timeout_error(self, mock_http_service):
        """Test G3 API timeout error handling."""
        # Arrange
        mock_http_service.post.side_effect = Timeout("Request timed out")
        service = G3AnyApiGlobalService(mock_http_service, 'Hilton')
        request = InputProcessingRequest('Hilton', 'TPANH')

        # Act & Assert
        with pytest.raises(Timeout):
            service.fetch(request)

    def test_g3_api_connection_error(self, mock_http_service):
        """Test G3 API connection error handling."""
        # Arrange
        mock_http_service.post.side_effect = ConnectionError("Connection failed")
        service = G3AnyApiGlobalService(mock_http_service, 'Hilton')
        request = InputProcessingRequest('Hilton', 'TPANH')

        # Act & Assert
        with pytest.raises(ConnectionError):
            service.fetch(request)

    def test_g3_api_invalid_response_format(self, mock_http_service):
        """Test G3 API invalid response format handling."""
        # Arrange
        mock_http_service.post.return_value = "invalid_response"
        service = G3AnyApiGlobalService(mock_http_service, 'Hilton')
        request = InputProcessingRequest('Hilton', 'TPANH')

        # Act & Assert
        with pytest.raises(Exception):  # pandas will raise an exception for invalid data
            service.fetch(request)

    def test_g3_api_empty_response(self, mock_http_service):
        """Test G3 API empty response handling."""
        # Arrange
        mock_http_service.post.return_value = []
        service = G3AnyApiGlobalService(mock_http_service, 'Hilton')
        request = InputProcessingRequest('Hilton', 'TPANH')

        # Act
        result = service.fetch(request)

        # Assert
        assert isinstance(result, pd.DataFrame)
        assert len(result) == 0
        assert list(result.columns) == [
            'clientCode', 'propertyCode', 'processingType', 'preparedDate', 'timeZone'
        ]

    def test_input_processing_request_query_generation(self):
        """Test InputProcessingRequest SQL query generation."""
        # Arrange
        request = InputProcessingRequest('Hilton', 'TPANH')

        # Act
        query = request.get_query()
        params = request.get_query_params()

        # Assert
        assert 'Hilton' in query
        assert 'TPANH' in query
        assert 'Input_Processing' in query
        assert params['clientCode'] == 'Hilton'
        assert params['propertyCode'] == 'TPANH'

    def test_property_id_request_query_generation(self):
        """Test PropertyIdRequest SQL query generation."""
        # Arrange
        request = PropertyIdRequest('Hilton', 'TPANH')

        # Act
        query = request.get_query()
        params = request.get_query_params()

        # Assert
        assert 'property' in query.lower()
        assert 'client' in query.lower()
        assert params['clientCode'] == 'Hilton'
        assert params['propertyCode'] == 'TPANH'

    def test_pace_accom_request_query_generation(self):
        """Test PaceAccomActivityRequest SQL query generation."""
        # Arrange
        start_date = datetime(2024, 1, 15)
        end_date = datetime(2024, 1, 16)
        request = PaceAccomActivityRequest(start_date, end_date)

        # Act
        query = request.get_query()
        params = request.get_query_params()

        # Assert
        assert 'pace_accom_activity' in query.lower()
        assert 'occupancy_dt' in query.lower()
        assert params['startDate'] == start_date
        assert params['endDate'] == end_date
