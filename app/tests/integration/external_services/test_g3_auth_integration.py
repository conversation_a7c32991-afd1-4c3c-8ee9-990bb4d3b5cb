"""
Integration tests for G3 authentication and HTTP service.

Tests:
- G3Auth authentication flow
- HttpService integration with G3Auth
- Token refresh mechanisms
- Authentication failure scenarios
- Secret manager integration
"""

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from requests import Request, HTTPError, Response
from requests.auth import AuthBase

from src.common.http_service import HttpService
from src.common.http_authorizers.g3_auth import G3Auth
from src.common.exceptions.http_exception import HttpException


class TestG3AuthenticationIntegration:
    """Test G3 authentication integration."""

    @pytest.fixture
    def mock_request(self):
        """Create a mock HTTP request."""
        request = Mock(spec=Request)
        request.headers = {}
        return request

    @pytest.fixture
    def sample_g3_auth_token(self):
        """Sample G3 authentication token."""
        return "dGVzdF91c2VyOnRlc3RfcGFzc3dvcmQ="  # base64 encoded test_user:test_password

    @patch('src.common.secret_manager.get_g3_auth_secret')
    @patch('src.common.env.get_value_or_default')
    def test_g3_auth_with_environment_token(
        self, 
        mock_get_value_or_default, 
        mock_get_secret,
        mock_request,
        sample_g3_auth_token
    ):
        """Test G3Auth when token is available in environment."""
        # Arrange
        mock_get_value_or_default.return_value = sample_g3_auth_token
        auth = G3Auth()

        # Act
        result = auth(mock_request)

        # Assert
        assert result == mock_request
        assert mock_request.headers['Authorization'] == f'Basic {sample_g3_auth_token}'
        mock_get_secret.assert_not_called()  # Should not call secret manager if env var exists

    @patch('src.common.secret_manager.get_g3_auth_secret')
    @patch('src.common.env.get_value_or_default')
    def test_g3_auth_with_secret_manager_fallback(
        self,
        mock_get_value_or_default,
        mock_get_secret,
        mock_request,
        sample_g3_auth_token
    ):
        """Test G3Auth fallback to secret manager when env var is not set."""
        # Arrange
        mock_get_value_or_default.side_effect = lambda env_var, fallback_func: fallback_func()
        mock_get_secret.return_value = sample_g3_auth_token
        auth = G3Auth()

        # Act
        result = auth(mock_request)

        # Assert
        assert result == mock_request
        assert mock_request.headers['Authorization'] == f'Basic {sample_g3_auth_token}'
        mock_get_secret.assert_called_once()

    @patch('src.common.secret_manager.get_g3_auth_secret')
    @patch('src.common.env.get_value_or_default')
    def test_g3_auth_secret_manager_failure(
        self,
        mock_get_value_or_default,
        mock_get_secret,
        mock_request
    ):
        """Test G3Auth when secret manager fails."""
        # Arrange
        mock_get_value_or_default.side_effect = lambda env_var, fallback_func: fallback_func()
        mock_get_secret.side_effect = Exception("Secret not found")
        auth = G3Auth()

        # Act & Assert
        with pytest.raises(Exception, match="Secret not found"):
            auth(mock_request)


class TestHttpServiceG3Integration:
    """Test HttpService integration with G3Auth."""

    @pytest.fixture
    def mock_g3_auth(self):
        """Create a mock G3Auth instance."""
        auth = Mock(spec=G3Auth)
        auth.refresh_token = Mock()
        return auth

    @pytest.fixture
    def http_service(self, mock_g3_auth):
        """Create HttpService with mock G3Auth."""
        return HttpService('https://g3.example.com', authorizer=mock_g3_auth)

    @patch('requests.post')
    def test_http_service_successful_post(self, mock_post, http_service, mock_g3_auth):
        """Test successful HTTP POST with G3 authentication."""
        # Arrange
        mock_response = Mock()
        mock_response.ok = True
        mock_response.status_code = 200
        mock_response.json.return_value = {'result': 'success'}
        mock_post.return_value = mock_response

        # Act
        result = http_service.post('api/test', {'query': 'SELECT * FROM test'})

        # Assert
        assert result == {'result': 'success'}
        mock_post.assert_called_once()
        call_args = mock_post.call_args
        assert call_args[0][0] == 'https://g3.example.com/api/test'
        assert call_args[1]['auth'] == mock_g3_auth
        assert json.loads(call_args[1]['data']) == {'query': 'SELECT * FROM test'}

    @patch('requests.post')
    def test_http_service_401_retry_mechanism(self, mock_post, http_service, mock_g3_auth):
        """Test HTTP service 401 retry mechanism with token refresh."""
        # Arrange
        # First call returns 401, second call succeeds
        mock_response_401 = Mock()
        mock_response_401.status_code = 401
        mock_response_401.ok = False

        mock_response_success = Mock()
        mock_response_success.status_code = 200
        mock_response_success.ok = True
        mock_response_success.json.return_value = {'result': 'success_after_retry'}

        mock_post.side_effect = [mock_response_401, mock_response_success]

        # Act
        result = http_service.post('api/test', {'query': 'SELECT * FROM test'})

        # Assert
        assert result == {'result': 'success_after_retry'}
        assert mock_post.call_count == 2
        mock_g3_auth.refresh_token.assert_called_once()

    @patch('requests.post')
    def test_http_service_non_401_error(self, mock_post, http_service, mock_g3_auth):
        """Test HTTP service handling of non-401 errors."""
        # Arrange
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.ok = False
        mock_response.reason = 'Internal Server Error'
        mock_post.return_value = mock_response

        # Act & Assert
        with pytest.raises(HttpException, match="Request to .* failed with status code 500"):
            http_service.post('api/test', {'query': 'SELECT * FROM test'})

        mock_g3_auth.refresh_token.assert_not_called()

    @patch('requests.get')
    def test_http_service_get_with_auth(self, mock_get, http_service, mock_g3_auth):
        """Test HTTP GET with G3 authentication."""
        # Arrange
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.ok = True
        mock_get.return_value = mock_response

        # Act
        result = http_service.get_raw_response('api/test', params={'param1': 'value1'})

        # Assert
        assert result == mock_response
        mock_get.assert_called_once()
        call_args = mock_get.call_args
        assert call_args[0][0] == 'https://g3.example.com/api/test'
        assert call_args[1]['auth'] == mock_g3_auth
        assert call_args[1]['params'] == {'param1': 'value1'}

    @patch('requests.get')
    def test_http_service_get_401_retry(self, mock_get, http_service, mock_g3_auth):
        """Test HTTP GET 401 retry mechanism."""
        # Arrange
        mock_response_401 = Mock()
        mock_response_401.status_code = 401

        mock_response_success = Mock()
        mock_response_success.status_code = 200

        mock_get.side_effect = [mock_response_401, mock_response_success]

        # Act
        result = http_service.get_raw_response('api/test')

        # Assert
        assert result == mock_response_success
        assert mock_get.call_count == 2
        mock_g3_auth.refresh_token.assert_called_once()

    def test_http_service_url_construction(self, mock_g3_auth):
        """Test HTTP service URL construction."""
        # Test with trailing slash
        service1 = HttpService('https://g3.example.com/', authorizer=mock_g3_auth)
        assert service1._HttpService__get_full_url('api/test') == 'https://g3.example.com/api/test'

        # Test without trailing slash
        service2 = HttpService('https://g3.example.com', authorizer=mock_g3_auth)
        assert service2._HttpService__get_full_url('api/test') == 'https://g3.example.com/api/test'

        # Test with path in base URL
        service3 = HttpService('https://g3.example.com/base', authorizer=mock_g3_auth)
        assert service3._HttpService__get_full_url('api/test') == 'https://g3.example.com/base/api/test'


class TestG3AuthSecretManagerIntegration:
    """Test G3Auth integration with AWS Secret Manager."""

    @patch('src.common.secret_manager.secretsmanager.get_secret')
    @patch('src.common.env.get_value')
    def test_secret_manager_successful_retrieval(self, mock_get_value, mock_get_secret):
        """Test successful secret retrieval from AWS Secret Manager."""
        # Arrange
        mock_get_value.return_value = 'test-secret-name'
        mock_secret_data = {
            'g3_auth_token': 'dGVzdF91c2VyOnRlc3RfcGFzc3dvcmQ='
        }
        mock_get_secret.return_value = json.dumps(mock_secret_data)

        # Import here to trigger the secret manager call
        from src.common.secret_manager import get_g3_auth_secret

        # Act
        result = get_g3_auth_secret()

        # Assert
        assert result == 'dGVzdF91c2VyOnRlc3RfcGFzc3dvcmQ='
        mock_get_secret.assert_called_once_with('test-secret-name')

    @patch('src.common.secret_manager.secretsmanager.get_secret')
    @patch('src.common.env.get_value')
    def test_secret_manager_missing_key(self, mock_get_value, mock_get_secret):
        """Test secret manager when key is missing."""
        # Arrange
        mock_get_value.return_value = 'test-secret-name'
        mock_secret_data = {
            'other_key': 'other_value'
        }
        mock_get_secret.return_value = json.dumps(mock_secret_data)

        from src.common.secret_manager import get_g3_auth_secret

        # Act & Assert
        with pytest.raises(KeyError):
            get_g3_auth_secret()

    @patch('src.common.secret_manager.secretsmanager.get_secret')
    @patch('src.common.env.get_value')
    def test_secret_manager_invalid_json(self, mock_get_value, mock_get_secret):
        """Test secret manager with invalid JSON."""
        # Arrange
        mock_get_value.return_value = 'test-secret-name'
        mock_get_secret.return_value = 'invalid_json'

        from src.common.secret_manager import get_g3_auth_secret

        # Act & Assert
        with pytest.raises(json.JSONDecodeError):
            get_g3_auth_secret()

    @patch('src.common.secret_manager.secretsmanager.get_secret')
    @patch('src.common.env.get_value')
    def test_secret_manager_aws_error(self, mock_get_value, mock_get_secret):
        """Test secret manager when AWS service fails."""
        # Arrange
        mock_get_value.return_value = 'test-secret-name'
        mock_get_secret.side_effect = Exception("AWS service unavailable")

        from src.common.secret_manager import get_g3_auth_secret

        # Act & Assert
        with pytest.raises(Exception, match="AWS service unavailable"):
            get_g3_auth_secret()
