#!/usr/bin/env python3
"""
G3 API Mock Service
Provides controlled test data for calibration workflow integration testing
"""

from fastapi import FastAP<PERSON>, HTTPException, Request
from fastapi.responses import JSONResponse
import pandas as pd
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any
import logging

logger = logging.getLogger(__name__)

app = FastAPI(title="G3 API Mock Service", version="1.0.0")

class G3ApiMockData:
    """Manages test data for G3 API mock responses"""
    
    def __init__(self):
        self.test_scenarios = {
            'standard': self._create_standard_scenario(),
            'high_volume': self._create_high_volume_scenario(),
            'edge_case': self._create_edge_case_scenario(),
            'error_scenario': self._create_error_scenario()
        }
        self.current_scenario = 'standard'
    
    def _create_standard_scenario(self) -> Dict[str, Any]:
        """Standard calibration test scenario"""
        base_date = datetime.now() - timedelta(days=7)
        
        # Input processing data
        input_processing_data = []
        for i in range(7):
            date_str = (base_date + timedelta(days=i)).strftime('%Y-%m-%d %H:%M:%S')
            input_processing_data.append({
                'client_code': 'TestClient',
                'property_code': 'TEST01',
                'input_type': 'CDP',
                'prepared_dttm': date_str,
                'property_time_zone': 'America/New_York'
            })
        
        # Property configuration
        property_config = {
            'client_code': 'TestClient',
            'property_code': 'TEST01',
            'property_id': 12345,
            'time_zone': 'America/New_York',
            'calibration_settings': {
                'rolling_window': 21,
                'use_leading_window': True,
                'pressure_floor': 0.1,
                'waste_threshold': 2.0,
                'regret_threshold': 5.0
            }
        }
        
        return {
            'input_processing': input_processing_data,
            'property_config': property_config
        }
    
    def _create_high_volume_scenario(self) -> Dict[str, Any]:
        """High volume test scenario with more data points"""
        base_date = datetime.now() - timedelta(days=30)
        
        input_processing_data = []
        for i in range(30):
            for hour in [6, 12, 18]:  # Multiple processing times per day
                date_str = (base_date + timedelta(days=i, hours=hour)).strftime('%Y-%m-%d %H:%M:%S')
                input_processing_data.append({
                    'client_code': 'TestClient',
                    'property_code': 'TEST01',
                    'input_type': 'CDP_DYNAMIC',
                    'prepared_dttm': date_str,
                    'property_time_zone': 'America/New_York'
                })
        
        property_config = {
            'client_code': 'TestClient',
            'property_code': 'TEST01',
            'property_id': 12345,
            'time_zone': 'America/New_York',
            'calibration_settings': {
                'rolling_window': 30,
                'use_leading_window': False,
                'pressure_floor': 0.05,
                'waste_threshold': 1.5,
                'regret_threshold': 8.0
            }
        }
        
        return {
            'input_processing': input_processing_data,
            'property_config': property_config
        }
    
    def _create_edge_case_scenario(self) -> Dict[str, Any]:
        """Edge case scenario with minimal data"""
        base_date = datetime.now() - timedelta(days=2)
        
        input_processing_data = [
            {
                'client_code': 'TestClient',
                'property_code': 'TEST01',
                'input_type': 'CDP',
                'prepared_dttm': base_date.strftime('%Y-%m-%d %H:%M:%S'),
                'property_time_zone': 'America/New_York'
            }
        ]
        
        property_config = {
            'client_code': 'TestClient',
            'property_code': 'TEST01',
            'property_id': 12345,
            'time_zone': 'America/New_York',
            'calibration_settings': {
                'rolling_window': 7,
                'use_leading_window': True,
                'pressure_floor': 0.2,
                'waste_threshold': 0.5,
                'regret_threshold': 1.0
            }
        }
        
        return {
            'input_processing': input_processing_data,
            'property_config': property_config
        }
    
    def _create_error_scenario(self) -> Dict[str, Any]:
        """Error scenario for testing error handling"""
        return {
            'input_processing': [],
            'property_config': None,
            'error': True
        }

# Global mock data instance
mock_data = G3ApiMockData()

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "G3 API Mock"}

@app.post("/api/G3Tables/executeQueryOnGlobal/v1")
async def execute_query_on_global(request: Request):
    """Mock G3 global query endpoint"""
    try:
        body = await request.json()
        query = body.get('query', '')
        
        # Parse query to determine response type
        if 'Input_Processing' in query:
            return _handle_input_processing_query(request)
        elif 'property_daily_processing' in query.lower():
            return _handle_property_processing_query(request)
        else:
            logger.warning(f"Unknown query type: {query}")
            return []
    
    except Exception as e:
        logger.error(f"Error processing G3 query: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/G3Tables/executeQuery/v1")
async def execute_query(request: Request):
    """Mock G3 property-specific query endpoint"""
    try:
        body = await request.json()
        query = body.get('query', '')
        
        # Handle different query types
        if 'pace_accom_activity' in query.lower():
            return _handle_pace_accom_activity_query(request)
        elif 'decision_analysis' in query.lower():
            return _handle_decision_analysis_query(request)
        else:
            logger.warning(f"Unknown property query type: {query}")
            return []
    
    except Exception as e:
        logger.error(f"Error processing G3 property query: {e}")
        raise HTTPException(status_code=500, detail=str(e))

def _handle_input_processing_query(request: Request) -> List[List[Any]]:
    """Handle input processing data query"""
    scenario_data = mock_data.test_scenarios[mock_data.current_scenario]
    
    if scenario_data.get('error'):
        raise HTTPException(status_code=500, detail="Mock G3 API error")
    
    # Convert to G3 API response format (list of lists)
    input_data = scenario_data['input_processing']
    response = []
    
    for item in input_data:
        response.append([
            item['client_code'],
            item['property_code'],
            item['input_type'],
            item['prepared_dttm'],
            item['property_time_zone']
        ])
    
    logger.info(f"Returning {len(response)} input processing records")
    return response

def _handle_property_processing_query(request: Request) -> List[List[Any]]:
    """Handle property processing configuration query"""
    scenario_data = mock_data.test_scenarios[mock_data.current_scenario]
    
    if scenario_data.get('error'):
        raise HTTPException(status_code=500, detail="Mock G3 API error")
    
    config = scenario_data['property_config']
    
    # Return property configuration data
    return [[
        config['client_code'],
        config['property_code'],
        config['property_id'],
        config['time_zone']
    ]]

def _handle_pace_accom_activity_query(request: Request) -> List[List[Any]]:
    """Handle pace accommodation activity query"""
    # Generate sample pace data
    base_date = datetime.now() - timedelta(days=7)
    response = []
    
    for i in range(7):
        for accom_class in [1, 2, 3]:
            for lead_time in [0, 1, 7, 14, 30]:
                occ_date = (base_date + timedelta(days=i)).strftime('%Y-%m-%d')
                response.append([
                    occ_date,  # Occupancy_DT
                    lead_time,  # leadTime
                    accom_class,  # accom_class_id
                    100,  # accom_capacity
                    75,  # roomsSold
                    25   # available_capacity
                ])
    
    return response

def _handle_decision_analysis_query(request: Request) -> List[List[Any]]:
    """Handle decision analysis query"""
    # Generate sample decision analysis data
    base_date = datetime.now() - timedelta(days=7)
    response = []
    
    for i in range(7):
        date_str = (base_date + timedelta(days=i)).strftime('%Y-%m-%d')
        response.append([
            'TestClient',  # clientCode
            'TEST01',      # propertyCode
            date_str,      # occupancyDate
            'Monday',      # dowName
            14,            # DTA
            date_str,      # decisionDate
            date_str,      # localDate
            'DYNAMIC',     # decisionTypeName
            'Test Decision', # name
            150.0,         # systemNewDecision
            140.0,         # systemOldDecision
            10.0,          # absoluteDifference
            7.14           # absolutePercentage
        ])
    
    return response

@app.post("/test/scenario/{scenario_name}")
async def set_test_scenario(scenario_name: str):
    """Set the current test scenario"""
    if scenario_name not in mock_data.test_scenarios:
        raise HTTPException(status_code=400, detail=f"Unknown scenario: {scenario_name}")
    
    mock_data.current_scenario = scenario_name
    logger.info(f"Set test scenario to: {scenario_name}")
    
    return {
        "message": f"Test scenario set to {scenario_name}",
        "available_scenarios": list(mock_data.test_scenarios.keys())
    }

@app.get("/test/scenario")
async def get_current_scenario():
    """Get the current test scenario"""
    return {
        "current_scenario": mock_data.current_scenario,
        "available_scenarios": list(mock_data.test_scenarios.keys())
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8080)
