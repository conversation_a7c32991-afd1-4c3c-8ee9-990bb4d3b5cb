#!/usr/bin/env python3
"""
G3 API Integration Test Runner

This script provides a convenient way to run G3 API integration tests
with various configurations and reporting options.

Usage:
    python run_g3_tests.py [options]

Examples:
    # Run all G3 API integration tests
    python run_g3_tests.py

    # Run only authentication tests
    python run_g3_tests.py --auth-only

    # Run with verbose output and coverage
    python run_g3_tests.py --verbose --coverage

    # Run specific test scenarios
    python run_g3_tests.py --scenario success,failure,timeout
"""

import argparse
import subprocess
import sys
import os
from pathlib import Path


class G3TestRunner:
    """Test runner for G3 API integration tests."""

    def __init__(self):
        self.test_dir = Path(__file__).parent
        self.app_dir = self.test_dir.parent.parent
        self.available_scenarios = [
            'success', 'failure', 'timeout', 'auth', 'empty_data', 
            'large_dataset', 'concurrent', 'comprehensive'
        ]

    def run_tests(self, args):
        """Run the specified tests."""
        cmd = ['python', '-m', 'pytest']
        
        # Add test paths based on arguments
        test_paths = self._get_test_paths(args)
        cmd.extend(test_paths)
        
        # Add pytest options
        cmd.extend(self._get_pytest_options(args))
        
        print(f"Running command: {' '.join(cmd)}")
        print(f"Working directory: {self.app_dir}")
        
        # Change to app directory and run tests
        os.chdir(self.app_dir)
        result = subprocess.run(cmd, capture_output=False)
        
        return result.returncode

    def _get_test_paths(self, args):
        """Get test paths based on arguments."""
        paths = []
        
        if args.auth_only:
            paths.append('tests/integration/external_services/test_g3_auth_integration.py')
        elif args.workflow_only:
            paths.append('tests/integration/workflows/test_g3_calibration_workflow.py')
        elif args.comprehensive_only:
            paths.append('tests/integration/external_services/test_g3_api_comprehensive.py')
        elif args.scenario:
            # Map scenarios to specific test methods
            scenario_map = {
                'success': 'test_successful_calibration_with_g3_data',
                'failure': 'test_g3_api_authentication_failure_handling',
                'timeout': 'test_g3_api_timeout_handling',
                'auth': 'tests/integration/external_services/test_g3_auth_integration.py',
                'empty_data': 'test_g3_api_empty_data_handling',
                'large_dataset': 'test_g3_api_large_dataset_performance',
                'concurrent': 'test_g3_api_concurrent_requests',
                'comprehensive': 'tests/integration/external_services/test_g3_api_comprehensive.py'
            }
            
            for scenario in args.scenario:
                if scenario in scenario_map:
                    test_path = scenario_map[scenario]
                    if test_path.startswith('test_'):
                        # It's a specific test method
                        paths.append(f'tests/integration/external_services/test_g3_api_comprehensive.py::{test_path}')
                    else:
                        # It's a file path
                        paths.append(test_path)
        else:
            # Run all G3 API integration tests
            paths.extend([
                'tests/integration/external_services/test_g3_api_integration.py',
                'tests/integration/external_services/test_g3_auth_integration.py',
                'tests/integration/external_services/test_g3_api_comprehensive.py',
                'tests/integration/workflows/test_g3_calibration_workflow.py'
            ])
        
        return paths

    def _get_pytest_options(self, args):
        """Get pytest options based on arguments."""
        options = []
        
        if args.verbose:
            options.append('-v')
        
        if args.coverage:
            options.extend(['--cov=src', '--cov-report=html', '--cov-report=term'])
        
        if args.parallel:
            options.extend(['-n', str(args.parallel)])
        
        if args.markers:
            for marker in args.markers:
                options.extend(['-m', marker])
        
        if args.capture == 'no':
            options.append('-s')
        elif args.capture == 'sys':
            options.append('--capture=sys')
        
        if args.tb_style:
            options.extend(['--tb', args.tb_style])
        
        if args.maxfail:
            options.extend(['--maxfail', str(args.maxfail)])
        
        if args.lf:
            options.append('--lf')
        
        if args.ff:
            options.append('--ff')
        
        return options

    def list_scenarios(self):
        """List available test scenarios."""
        print("Available test scenarios:")
        for scenario in self.available_scenarios:
            print(f"  - {scenario}")

    def validate_scenarios(self, scenarios):
        """Validate that all specified scenarios exist."""
        invalid = [s for s in scenarios if s not in self.available_scenarios]
        if invalid:
            print(f"Error: Invalid scenarios: {', '.join(invalid)}")
            print("Available scenarios:")
            self.list_scenarios()
            return False
        return True


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description='Run G3 API integration tests',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    # Test selection options
    parser.add_argument(
        '--auth-only', 
        action='store_true',
        help='Run only authentication tests'
    )
    parser.add_argument(
        '--workflow-only', 
        action='store_true',
        help='Run only workflow integration tests'
    )
    parser.add_argument(
        '--comprehensive-only', 
        action='store_true',
        help='Run only comprehensive scenario tests'
    )
    parser.add_argument(
        '--scenario', 
        nargs='+',
        choices=['success', 'failure', 'timeout', 'auth', 'empty_data', 
                'large_dataset', 'concurrent', 'comprehensive'],
        help='Run specific test scenarios'
    )
    
    # Pytest options
    parser.add_argument(
        '-v', '--verbose', 
        action='store_true',
        help='Verbose output'
    )
    parser.add_argument(
        '--coverage', 
        action='store_true',
        help='Generate coverage report'
    )
    parser.add_argument(
        '-n', '--parallel', 
        type=int,
        help='Run tests in parallel (number of workers)'
    )
    parser.add_argument(
        '-m', '--markers', 
        nargs='+',
        help='Run tests with specific markers'
    )
    parser.add_argument(
        '--capture', 
        choices=['no', 'sys'],
        help='Capture mode for output'
    )
    parser.add_argument(
        '--tb', 
        dest='tb_style',
        choices=['short', 'long', 'line', 'native'],
        default='short',
        help='Traceback style'
    )
    parser.add_argument(
        '--maxfail', 
        type=int,
        help='Stop after N failures'
    )
    parser.add_argument(
        '--lf', 
        action='store_true',
        help='Run last failed tests'
    )
    parser.add_argument(
        '--ff', 
        action='store_true',
        help='Run failed tests first'
    )
    
    # Utility options
    parser.add_argument(
        '--list-scenarios', 
        action='store_true',
        help='List available test scenarios'
    )
    
    args = parser.parse_args()
    
    runner = G3TestRunner()
    
    if args.list_scenarios:
        runner.list_scenarios()
        return 0
    
    if args.scenario and not runner.validate_scenarios(args.scenario):
        return 1
    
    return runner.run_tests(args)


if __name__ == '__main__':
    sys.exit(main())
