import logging
import pathlib
from datetime import date, timedelta
from typing import Callable

import pandas as pd

from src.algorithm.Utilities.ElevateUtilities.ElevateDefaultParams import ElevateDefaultParams
from src.algorithm.Utilities.GenericUtilities.GenericObjFileLoaders import GenericDeltaLoader
from src.algorithm.dynamic_optimization_package.EvaluateDynamicOptimization import EvaluateDynamicOptimization
from src.common.dto.evaluation_result import EvaluationResult
from src.common.dynamodb_service import dynamodb_service
from src.common.ups_service import get_property_timezone
from src.common.utilities.DateUtil import DateUtil

logger = logging.getLogger(__name__)
_MAX_OPT_WINDOW = 365
def run_evaluation(file_reader: Callable, delta_occ_solds: pathlib.Path | str, reference_rate: pathlib.Path| str,
                   available_capacity: pathlib.Path| str, client_code: str, property_code: str,
                   delta_lrv: pathlib.Path| str, window_size: int, calibrated_value: dict[int | str, float],
                   use_leading_window=False, fail_on_missing_occ_dates=True, pressure_floor:float = 0,
                   minHeuristicOccPercChangeThreshold:float=0., persist_eval_op_at_rc_lvl=True, max_idp_recom_cnt = -1,
                   total_scheduled_idp_count = -1, idp_count_current_day = lambda t:pd.DataFrame(columns=['count'])):
    jan_1st_1970 = date(1970, 1, 1)
    delta_occ_solds_df = dynamodb_service.fetch_inc_solds(client_code, property_code)
    if len(calibrated_value) == 0 or len(delta_occ_solds_df) == 0:
        logger.info("No calibrated values")
        return EvaluationResult(should_optimize=False, max_occupancy_date=None, rc_to_occupancy_date_mapping={}), jan_1st_1970, pd.DataFrame()
    lrv_df = file_reader(delta_lrv)
    capture_date = lrv_df['captureDate'].unique()[0]
    client_code = lrv_df['clientCode'].iloc[0]
    property_code = lrv_df['propertyCode'].iloc[0]
    property_timezone = get_property_timezone(client_code, property_code)
    property_date = DateUtil.get_current_date_for_timezone(property_timezone)
    if property_date != capture_date:
        logger.info(f'Property date has rolled over, evaluation ignored. Property Date: {property_date}, Capture Date: {capture_date}.')
        return EvaluationResult(should_optimize=False, max_occupancy_date=None, rc_to_occupancy_date_mapping={}), jan_1st_1970, pd.DataFrame()

    idp_count_df = idp_count_current_day(capture_date)
    if max_idp_recom_cnt > 0 and not idp_count_df.empty:
        idp_count =  int(idp_count_df['count'].iloc[0])
        if idp_count >= max_idp_recom_cnt:
            logger.info(f'Current days IDP recommendation limit {max_idp_recom_cnt} already reached')
            return EvaluationResult(should_optimize=False, max_occupancy_date=None, rc_to_occupancy_date_mapping={}), jan_1st_1970, pd.DataFrame()
        elif max_idp_recom_cnt > 1 and idp_count + total_scheduled_idp_count >= max_idp_recom_cnt-1:
           return EvaluationResult(should_optimize=False, max_occupancy_date=None, rc_to_occupancy_date_mapping={}), jan_1st_1970, pd.DataFrame()

    avl_cap: pd.DataFrame = file_reader(available_capacity)
    occupancy_manager = GenericDeltaLoader.loadDeltaOccFromDeltaOccFile(filepath=delta_occ_solds,
                                                                        filereader=lambda t: add_lead_time_and_join_avl_cap(delta_occ_solds_df, lrv_df, avl_cap),
                                                                        defaultRC=ElevateDefaultParams.defaultRoomClass,
                                                                        rcacCol='accomClassId',
                                                                        ltCol='leadTime',
                                                                        deltaOccCol='deltaSolds',
                                                                        dateCol='occupancyDate',
                                                                        capDateCol='captureDate',
                                                                        remainingCapacityCol='availableCapacity'
                                                                        )
    ref_price_manager = GenericDeltaLoader.loadRefRateFile(filepath=reference_rate,
                                                           filereader=file_reader,
                                                           defaultRC=ElevateDefaultParams.defaultRoomClass,
                                                           rcacCol='accomClassId',
                                                           ltCol='leadTime',
                                                           refRateCol='price',
                                                           dateCol='occupancyDate',
                                                           capDateCol='captureDate')
    lrv_delta_manager = GenericDeltaLoader.loadDeltaLRVFromDeltaLRVFile(filepath=delta_lrv,
                                                                        filereader=lambda t: lrv_df,
                                                                        defaultRC=ElevateDefaultParams.defaultRoomClass,
                                                                        rcacCol='accomClassId',
                                                                        ltCol='leadTime',
                                                                        deltaLRVCol='deltaLRV',
                                                                        dateCol='occupancyDate',
                                                                        capDateCol='captureDate',
                                                                        ceilCol = 'ceilingValue',
                                                                        lrvCol = 'lrv'
                                                                        )
    evaluator = EvaluateDynamicOptimization(deltaOccHandlerDict=occupancy_manager,
                                            refRateHandlerDict=ref_price_manager,
                                            deltaLRVHandlerDict=lrv_delta_manager,
                                            calibrated=calibrated_value,
                                            potentialMode='slidingwindow',
                                            windowSize=window_size,
                                            useLeadingWindow=use_leading_window,
                                            fail_on_missing_occ_dates=fail_on_missing_occ_dates,
                                            pressure_floor=pressure_floor,
                                            minHeuristicOccPercChangeThreshold=minHeuristicOccPercChangeThreshold
                                            )
    evaluator.__toggleDebug__()
    result, assemblage = evaluator.evaluate()
    max_date = jan_1st_1970
    print(result)
    rc_to_occupancy_date_mapping = {}
    for rc, v in result.items():
        print(v)
        print(rc)

        if persist_eval_op_at_rc_lvl and v[0]:
            rc_to_occupancy_date_mapping[rc] = v[1]

        if v[0] == True and max_date < v[1]:
            max_date = v[1]

    if max_date == jan_1st_1970:
        return EvaluationResult(should_optimize=False, max_occupancy_date=None, rc_to_occupancy_date_mapping=rc_to_occupancy_date_mapping), capture_date, assemblage

    max_date = max(max_date, DateUtil.str_to_dt(capture_date) + timedelta(days=_MAX_OPT_WINDOW - 1))
    return EvaluationResult(should_optimize=True, max_occupancy_date=max_date, rc_to_occupancy_date_mapping=rc_to_occupancy_date_mapping), capture_date, assemblage


def add_lead_time_and_join_avl_cap(delta_solds_df, lrv_df, avl_cap: pd.DataFrame):
    if len(lrv_df) == 0:
        return pd.DataFrame(columns=['occupancyDate', 'captureDate', 'leadTime', 'deltaSolds', 'accomClassId', 'captureDate', 'availableCapacity'])
    delta_solds_df['captureDate'] = lrv_df['captureDate'].unique()[0]
    delta_solds_df['leadTime'] = (pd.to_datetime(delta_solds_df['occupancyDate']) - pd.to_datetime(
        delta_solds_df['captureDate'])).dt.days
    if 'deltaSolds' in delta_solds_df.columns:
        delta_solds_df =  delta_solds_df
        return delta_solds_df.merge(avl_cap, on=['accomClassId', 'occupancyDate', 'captureDate', 'leadTime'])
    delta_solds_df: pd.DataFrame = delta_solds_df.rename(columns={'incSolds': 'deltaSolds'})
    return delta_solds_df.merge(avl_cap, on=['accomClassId', 'occupancyDate', 'captureDate', 'leadTime'])